2025-07-16 08:02:54 | INFO | MetruyenScraper initialized
2025-07-16 08:02:54 | INFO | ✅ Scraper configured
2025-07-16 08:02:54 | INFO | Initializing AI enhancement service...
2025-07-16 08:02:54 | INFO | Initialized Gemini model: gemini-2.5-flash
2025-07-16 08:02:54 | INFO | ✅ AI enhancement service initialized successfully
2025-07-16 08:02:54 | INFO | ✅ Enhancement service initialized
2025-07-16 08:02:54 | INFO | ✅ Application startup completed
2025-07-16 08:03:17 | INFO | 🔵 GET /docs - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:03:17 | INFO | ✅ GET /docs - Status: 200 - Time: 0.001s - Size: 937
2025-07-16 08:03:17 | INFO | 🔵 GET /openapi.json - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:03:17 | INFO | ✅ GET /openapi.json - Status: 200 - Time: 0.028s - Size: 55166
2025-07-16 08:05:14 | INFO | 🔵 POST /api/v1/batch-scraping/chapters - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:05:14 | INFO | ✅ POST /api/v1/batch-scraping/chapters - Status: 200 - Time: 0.004s - Size: 279
2025-07-16 08:05:14 | ERROR | Unhandled exception: 'EnhancedBatchScrapingService' object has no attribute 'log_error'
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\batch_scraping.py", line 94, in validate_chapters
    chapter = await self.db.chapters.find_one({"_id": chapter_object_id})
                    │    │                            └ ObjectId('6874c5ecd2a3c69ee3d8b21a')
                    │    └ None
                    └ <API.routers.batch_scraping.EnhancedBatchScrapingService object at 0x000001D41A1AECF0>

AttributeError: 'NoneType' object has no attribute 'chapters'


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\main.py", line 19, in <module>
    uvicorn.run("API.main:app", host="0.0.0.0", port=8002, reload=False)
    │       └ <function run at 0x000001D4165FFEC0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uvicorn\\__init__.py'>

  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x000001D41663EF20>
    └ <uvicorn.server.Server object at 0x000001D442556F90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001D41663EFC0>
           │       │   └ <uvicorn.server.Server object at 0x000001D442556F90>
           │       └ <function run at 0x000001D415A1FCE0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001D418A073E0>
           │      └ <function Runner.run at 0x000001D415B3A200>
           └ <asyncio.runners.Runner object at 0x000001D442557230>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001D415B23D80>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001D442557230>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001D415B23CE0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001D415B39B20>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001D4159AC860>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001D4425567B0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001D442...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001D...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000001D418A2B110>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001D4425567B0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001D442...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001D...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001D442...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001D...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001D442A66510>
          └ <fastapi.applications.FastAPI object at 0x000001D418A2B110>
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001D442CD8680>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001D...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          │    └ <API.middleware.logging.LoggingMiddleware object at 0x000001D442A663C0>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001D442A66510>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 183, in __call__
    raise app_exc
          └ AttributeError("'EnhancedBatchScrapingService' object has no attribute 'log_error'")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001D4432E7420>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D442CD84A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          │    └ <API.middleware.rate_limiting.RateLimitingMiddleware object at 0x000001D442A66270>
          └ <API.middleware.logging.LoggingMiddleware object at 0x000001D442A663C0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 183, in __call__
    raise app_exc
          └ AttributeError("'EnhancedBatchScrapingService' object has no attribute 'log_error'")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001D4432E7600>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D4432E6D40>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          │    └ <API.middleware.error_handling.ErrorHandlingMiddleware object at 0x000001D442A66120>
          └ <API.middleware.rate_limiting.RateLimitingMiddleware object at 0x000001D442A66270>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 183, in __call__
    raise app_exc
          └ AttributeError("'EnhancedBatchScrapingService' object has no attribute 'log_error'")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001D4432E6CA0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D4432E7100>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000001D442A65FD0>
          └ <API.middleware.error_handling.ErrorHandlingMiddleware object at 0x000001D442A66120>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8002', 'connection': 'keep-alive', 'content-length': '155', 'sec-ch-ua-platform': '"Windows"', 'u...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001D4432E6CA0>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D4432E7100>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x000001D416E78CC0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001D442A65FD0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001D442A65FD0...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D4432E7100>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001D442A65E80>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001D442A65FD0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001D442A65FD0...
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D4432E7100>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001D442A8BBB0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001D418A37D40>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001D442A65E80>
          └ <function wrap_app_handling_exceptions at 0x000001D416DC6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001D4432E7380>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D4432E7100>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001D418A37D40>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001D4432E7380>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D4432E7100>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001D418A37D40>>
          └ <fastapi.routing.APIRouter object at 0x000001D418A37D40>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001D4432E7380>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D4432E7100>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          │     └ <function Route.handle at 0x000001D416DEC2C0>
          └ APIRoute(path='/api/v1/batch-scraping/chapters', name='batch_scrape_chapters', methods=['POST'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001D4432E7380>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D4432E7100>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          │    └ <function request_response.<locals>.app at 0x000001D442ADCCC0>
          └ APIRoute(path='/api/v1/batch-scraping/chapters', name='batch_scrape_chapters', methods=['POST'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001D4432E7380>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D4432E7100>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000001D442D05C70>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000001D4432E7560>
          └ <function wrap_app_handling_exceptions at 0x000001D416DC6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001D4432E77E0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D4432E7100>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000001D4432E7560>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 74, in app
    await response(scope, receive, send)
          │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001D4432E77E0>
          │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D4432E7100>
          │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          └ <starlette.responses.JSONResponse object at 0x000001D442CC2FD0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\responses.py", line 160, in __call__
    await self.background()
          │    └ <fastapi.background.BackgroundTasks object at 0x000001D442CC67B0>
          └ <starlette.responses.JSONResponse object at 0x000001D442CC2FD0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\background.py", line 41, in __call__
    await task()
          └ <starlette.background.BackgroundTask object at 0x000001D442CC5160>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\background.py", line 26, in __call__
    await self.func(*self.args, **self.kwargs)
          │    │     │    │       │    └ {}
          │    │     │    │       └ <starlette.background.BackgroundTask object at 0x000001D442CC5160>
          │    │     │    └ (['6874c5ecd2a3c69ee3d8b21a', '6874c5ecd2a3c69ee3d8b21b'], 3, 2.0, <starlette.requests.Request object at 0x000001D442D05C70>,...
          │    │     └ <starlette.background.BackgroundTask object at 0x000001D442CC5160>
          │    └ <bound method EnhancedBatchScrapingService.scrape_chapters_batch of <API.routers.batch_scraping.EnhancedBatchScrapingService ...
          └ <starlette.background.BackgroundTask object at 0x000001D442CC5160>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\batch_scraping.py", line 136, in scrape_chapters_batch
    validation_result = await self.validate_chapters(chapter_ids)
                              │    │                 └ ['6874c5ecd2a3c69ee3d8b21a', '6874c5ecd2a3c69ee3d8b21b']
                              │    └ <function EnhancedBatchScrapingService.validate_chapters at 0x000001D41A2FEDE0>
                              └ <API.routers.batch_scraping.EnhancedBatchScrapingService object at 0x000001D41A1AECF0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\batch_scraping.py", line 114, in validate_chapters
    self.log_error(f"Error validating chapter {chapter_id}: {str(e)}")
    │                                          └ '6874c5ecd2a3c69ee3d8b21a'
    └ <API.routers.batch_scraping.EnhancedBatchScrapingService object at 0x000001D41A1AECF0>

AttributeError: 'EnhancedBatchScrapingService' object has no attribute 'log_error'
2025-07-16 08:09:01 | INFO | 🔵 GET /api/v1/stories - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:09:01 | INFO | 🔄 GET /api/v1/stories - Status: 307 - Time: 0.002s - Size: 0
2025-07-16 08:09:01 | INFO | 🔵 GET /api/v1/stories - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:09:01 | INFO | 🔄 GET /api/v1/stories - Status: 307 - Time: 0.001s - Size: 0
2025-07-16 08:09:01 | INFO | 🔵 GET /api/v1/stories/ - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:09:01 | INFO | Getting story list - page: 1, size: 12
2025-07-16 08:09:01 | INFO | ✅ GET /api/v1/stories/ - Status: 200 - Time: 0.007s - Size: 778
2025-07-16 08:09:01 | INFO | 🔵 GET /api/v1/stories/ - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:09:01 | INFO | Getting story list - page: 1, size: 12
2025-07-16 08:09:01 | INFO | ✅ GET /api/v1/stories/ - Status: 200 - Time: 0.005s - Size: 778
2025-07-16 08:11:58 | INFO | 🔵 GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:11:58 | INFO | 🔵 GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b/chapters - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:11:58 | INFO | Getting story details for: 6874cf0fd2a3c69ee3d8b22b
2025-07-16 08:11:58 | INFO | Getting chapter list for story: 6874cf0fd2a3c69ee3d8b22b
2025-07-16 08:11:58 | INFO | ✅ GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b - Status: 200 - Time: 0.009s - Size: 2088
2025-07-16 08:11:58 | INFO | 🔵 GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:11:58 | INFO | Getting story details for: 6874cf0fd2a3c69ee3d8b22b
2025-07-16 08:11:58 | INFO | ✅ GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b - Status: 200 - Time: 0.007s - Size: 2088
2025-07-16 08:11:58 | INFO | ✅ GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b/chapters - Status: 200 - Time: 0.022s - Size: 31157
2025-07-16 08:11:58 | INFO | 🔵 GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b/chapters - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:11:58 | INFO | Getting chapter list for story: 6874cf0fd2a3c69ee3d8b22b
2025-07-16 08:11:58 | INFO | ✅ GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b/chapters - Status: 200 - Time: 0.012s - Size: 31157
2025-07-16 08:11:59 | INFO | 🔵 GET /api/v1/stories - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:11:59 | INFO | 🔄 GET /api/v1/stories - Status: 307 - Time: 0.001s - Size: 0
2025-07-16 08:11:59 | INFO | 🔵 GET /api/v1/stories - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:11:59 | INFO | 🔵 GET /api/v1/stories/ - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:11:59 | INFO | Getting story list - page: 1, size: 12
2025-07-16 08:11:59 | INFO | 🔄 GET /api/v1/stories - Status: 307 - Time: 0.003s - Size: 0
2025-07-16 08:11:59 | INFO | ✅ GET /api/v1/stories/ - Status: 200 - Time: 0.007s - Size: 778
2025-07-16 08:11:59 | INFO | 🔵 GET /api/v1/stories/ - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:11:59 | INFO | Getting story list - page: 1, size: 12
2025-07-16 08:11:59 | INFO | ✅ GET /api/v1/stories/ - Status: 200 - Time: 0.006s - Size: 778
2025-07-16 08:14:53 | INFO | 🔵 GET /api/v1/stories - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:14:53 | INFO | 🔄 GET /api/v1/stories - Status: 307 - Time: 0.001s - Size: 0
2025-07-16 08:14:53 | INFO | 🔵 GET /api/v1/stories - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:14:53 | INFO | 🔄 GET /api/v1/stories - Status: 307 - Time: 0.002s - Size: 0
2025-07-16 08:14:53 | INFO | 🔵 GET /api/v1/stories/ - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:14:53 | INFO | Getting story list - page: 1, size: 12
2025-07-16 08:14:53 | INFO | ✅ GET /api/v1/stories/ - Status: 200 - Time: 0.007s - Size: 778
2025-07-16 08:14:53 | INFO | 🔵 GET /api/v1/stories/ - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:14:53 | INFO | Getting story list - page: 1, size: 12
2025-07-16 08:14:53 | INFO | ✅ GET /api/v1/stories/ - Status: 200 - Time: 0.005s - Size: 778
2025-07-16 08:15:39 | INFO | 🔵 POST /api/v1/batch-scraping/chapters - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:15:39 | INFO | ✅ POST /api/v1/batch-scraping/chapters - Status: 200 - Time: 0.005s - Size: 279
2025-07-16 08:15:39 | ERROR | Unhandled exception: 'EnhancedBatchScrapingService' object has no attribute 'log_error'
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\batch_scraping.py", line 94, in validate_chapters
    try:

AttributeError: 'NoneType' object has no attribute 'chapters'


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\main.py", line 19, in <module>
    uvicorn.run("API.main:app", host="0.0.0.0", port=8002, reload=False)
    │       └ <function run at 0x000001D4165FFEC0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uvicorn\\__init__.py'>

  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x000001D41663EF20>
    └ <uvicorn.server.Server object at 0x000001D442556F90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001D41663EFC0>
           │       │   └ <uvicorn.server.Server object at 0x000001D442556F90>
           │       └ <function run at 0x000001D415A1FCE0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001D418A073E0>
           │      └ <function Runner.run at 0x000001D415B3A200>
           └ <asyncio.runners.Runner object at 0x000001D442557230>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001D415B23D80>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001D442557230>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001D415B23CE0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001D415B39B20>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001D4159AC860>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001D4425567B0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001D443...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001D...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000001D418A2B110>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001D4425567B0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001D443...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001D...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001D443...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001D...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001D442A66510>
          └ <fastapi.applications.FastAPI object at 0x000001D418A2B110>
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001D4432E74C0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001D...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          │    └ <API.middleware.logging.LoggingMiddleware object at 0x000001D442A663C0>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001D442A66510>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 183, in __call__
    raise app_exc
          └ AttributeError("'EnhancedBatchScrapingService' object has no attribute 'log_error'")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001D4432E7A60>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D4432E76A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          │    └ <API.middleware.rate_limiting.RateLimitingMiddleware object at 0x000001D442A66270>
          └ <API.middleware.logging.LoggingMiddleware object at 0x000001D442A663C0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 183, in __call__
    raise app_exc
          └ AttributeError("'EnhancedBatchScrapingService' object has no attribute 'log_error'")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001D443292CA0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D4432E7E20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          │    └ <API.middleware.error_handling.ErrorHandlingMiddleware object at 0x000001D442A66120>
          └ <API.middleware.rate_limiting.RateLimitingMiddleware object at 0x000001D442A66270>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 183, in __call__
    raise app_exc
          └ AttributeError("'EnhancedBatchScrapingService' object has no attribute 'log_error'")
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001D443293F60>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D443293060>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000001D442A65FD0>
          └ <API.middleware.error_handling.ErrorHandlingMiddleware object at 0x000001D442A66120>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8002', 'connection': 'keep-alive', 'content-length': '155', 'sec-ch-ua-platform': '"Windows"', 'u...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001D443293F60>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D443293060>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x000001D416E78CC0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001D442A65FD0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001D442A65FD0...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D443293060>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001D442A65E80>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001D442A65FD0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001D442A65FD0...
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D443293060>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001D442D126D0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001D418A37D40>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001D442A65E80>
          └ <function wrap_app_handling_exceptions at 0x000001D416DC6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001D4432916C0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D443293060>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001D418A37D40>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001D4432916C0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D443293060>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001D418A37D40>>
          └ <fastapi.routing.APIRouter object at 0x000001D418A37D40>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001D4432916C0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D443293060>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          │     └ <function Route.handle at 0x000001D416DEC2C0>
          └ APIRoute(path='/api/v1/batch-scraping/chapters', name='batch_scrape_chapters', methods=['POST'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001D4432916C0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D443293060>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          │    └ <function request_response.<locals>.app at 0x000001D442ADCCC0>
          └ APIRoute(path='/api/v1/batch-scraping/chapters', name='batch_scrape_chapters', methods=['POST'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001D4432916C0>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D443293060>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000001D442D107E0>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000001D443290D60>
          └ <function wrap_app_handling_exceptions at 0x000001D416DC6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001D443291440>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D443293060>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000001D443290D60>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 74, in app
    await response(scope, receive, send)
          │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001D443291440>
          │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001D443293060>
          │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8002), 'cl...
          └ <starlette.responses.JSONResponse object at 0x000001D4439A9B70>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\responses.py", line 160, in __call__
    await self.background()
          │    └ <fastapi.background.BackgroundTasks object at 0x000001D442CC3610>
          └ <starlette.responses.JSONResponse object at 0x000001D4439A9B70>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\background.py", line 41, in __call__
    await task()
          └ <starlette.background.BackgroundTask object at 0x000001D442CC0E10>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\background.py", line 26, in __call__
    await self.func(*self.args, **self.kwargs)
          │    │     │    │       │    └ {}
          │    │     │    │       └ <starlette.background.BackgroundTask object at 0x000001D442CC0E10>
          │    │     │    └ (['6874c5ecd2a3c69ee3d8b21a', '6874c5ecd2a3c69ee3d8b21b'], 3, 2.0, <starlette.requests.Request object at 0x000001D442D107E0>,...
          │    │     └ <starlette.background.BackgroundTask object at 0x000001D442CC0E10>
          │    └ <bound method EnhancedBatchScrapingService.scrape_chapters_batch of <API.routers.batch_scraping.EnhancedBatchScrapingService ...
          └ <starlette.background.BackgroundTask object at 0x000001D442CC0E10>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\batch_scraping.py", line 136, in scrape_chapters_batch
    """Scrape multiple chapters with enhanced progress tracking"""

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\batch_scraping.py", line 114, in validate_chapters
    'title': chapter.get('title', ''),

AttributeError: 'EnhancedBatchScrapingService' object has no attribute 'log_error'
2025-07-16 08:15:51 | INFO | 🛑 Shutting down Vietnamese Web Novel API...
2025-07-16 08:15:51 | INFO | MetruyenScraper closed
2025-07-16 08:15:51 | INFO | ✅ AI enhancement service cleaned up
2025-07-16 08:15:51 | INFO | ✅ Services cleaned up
2025-07-16 08:15:51 | INFO | ✅ Application shutdown completed
2025-07-16 08:15:56 | INFO | MetruyenScraper initialized
2025-07-16 08:15:56 | INFO | ✅ Scraper configured
2025-07-16 08:15:56 | INFO | Initializing AI enhancement service...
2025-07-16 08:15:56 | INFO | Initialized Gemini model: gemini-2.5-flash
2025-07-16 08:15:56 | INFO | ✅ AI enhancement service initialized successfully
2025-07-16 08:15:56 | INFO | ✅ Enhancement service initialized
2025-07-16 08:15:56 | INFO | ✅ Application startup completed
2025-07-16 08:16:05 | INFO | 🔵 POST /api/v1/batch-scraping/chapters - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:16:05 | INFO | ⚠️ POST /api/v1/batch-scraping/chapters - Status: 404 - Time: 0.001s - Size: 74
2025-07-16 08:17:35 | INFO | 🔵 GET /docs - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:17:35 | INFO | ✅ GET /docs - Status: 200 - Time: 0.003s - Size: 937
2025-07-16 08:17:35 | INFO | 🔵 GET /openapi.json - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:17:35 | INFO | ✅ GET /openapi.json - Status: 200 - Time: 0.027s - Size: 55174
2025-07-16 08:17:52 | INFO | 🔵 POST /api/v1/batch-scraping/scrape-chapters - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:17:53 | ERROR | Unexpected Error: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None - Path: /api/v1/batch-scraping/scrape-chapters - Method: POST
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 148, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\middleware\error_handling.py", line 31, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 291, in app
    solved_result = await solve_dependencies(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\dependencies\utils.py", line 640, in solve_dependencies
    solved = await run_in_threadpool(call, **solved_result.values)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\concurrency.py", line 37, in run_in_threadpool
    return await anyio.to_thread.run_sync(func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\anyio\to_thread.py", line 56, in run_sync
    return await get_async_backend().run_sync_in_worker_thread(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        func, args, abandon_on_cancel=abandon_on_cancel, limiter=limiter
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\anyio\_backends\_asyncio.py", line 2470, in run_sync_in_worker_thread
    return await future
           ^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\anyio\_backends\_asyncio.py", line 967, in run
    result = context.run(func, *args)
  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\batch_scraping.py", line 319, in get_batch_scraping_service
    if not db_manager.database:
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\database.py", line 342, in __bool__
    raise NotImplementedError(
    ...<3 lines>...
    )
NotImplementedError: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None

2025-07-16 08:17:53 | INFO | ❌ POST /api/v1/batch-scraping/scrape-chapters - Status: 500 - Time: 0.059s - Size: 129
2025-07-16 08:32:22 | INFO | 🔵 POST /api/v1/batch-scraping/scrape-chapters - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:32:22 | ERROR | Unexpected Error: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None - Path: /api/v1/batch-scraping/scrape-chapters - Method: POST
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 148, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\middleware\error_handling.py", line 31, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 291, in app
    solved_result = await solve_dependencies(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\dependencies\utils.py", line 640, in solve_dependencies
    solved = await run_in_threadpool(call, **solved_result.values)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\concurrency.py", line 37, in run_in_threadpool
    return await anyio.to_thread.run_sync(func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\anyio\to_thread.py", line 56, in run_sync
    return await get_async_backend().run_sync_in_worker_thread(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        func, args, abandon_on_cancel=abandon_on_cancel, limiter=limiter
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\anyio\_backends\_asyncio.py", line 2470, in run_sync_in_worker_thread
    return await future
           ^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\anyio\_backends\_asyncio.py", line 967, in run
    result = context.run(func, *args)
  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\batch_scraping.py", line 319, in get_batch_scraping_service
    if db_manager.database is None:
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\database.py", line 342, in __bool__
    raise NotImplementedError(
    ...<3 lines>...
    )
NotImplementedError: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None

2025-07-16 08:32:22 | INFO | ❌ POST /api/v1/batch-scraping/scrape-chapters - Status: 500 - Time: 0.005s - Size: 129
2025-07-16 08:32:29 | INFO | 🔵 GET /docs - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:32:29 | INFO | ✅ GET /docs - Status: 200 - Time: 0.001s - Size: 937
2025-07-16 08:32:29 | INFO | 🔵 GET /openapi.json - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:32:29 | INFO | ✅ GET /openapi.json - Status: 200 - Time: 0.001s - Size: 55174
2025-07-16 08:32:34 | INFO | 🔵 POST /api/v1/batch-scraping/scrape-chapters - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:32:34 | ERROR | Unexpected Error: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None - Path: /api/v1/batch-scraping/scrape-chapters - Method: POST
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 148, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\middleware\error_handling.py", line 31, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 291, in app
    solved_result = await solve_dependencies(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\dependencies\utils.py", line 640, in solve_dependencies
    solved = await run_in_threadpool(call, **solved_result.values)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\concurrency.py", line 37, in run_in_threadpool
    return await anyio.to_thread.run_sync(func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\anyio\to_thread.py", line 56, in run_sync
    return await get_async_backend().run_sync_in_worker_thread(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        func, args, abandon_on_cancel=abandon_on_cancel, limiter=limiter
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\anyio\_backends\_asyncio.py", line 2470, in run_sync_in_worker_thread
    return await future
           ^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\anyio\_backends\_asyncio.py", line 967, in run
    result = context.run(func, *args)
  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\batch_scraping.py", line 319, in get_batch_scraping_service
    if db_manager.database is None:
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\database.py", line 342, in __bool__
    raise NotImplementedError(
    ...<3 lines>...
    )
NotImplementedError: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None

2025-07-16 08:32:34 | INFO | ❌ POST /api/v1/batch-scraping/scrape-chapters - Status: 500 - Time: 0.005s - Size: 129
2025-07-16 08:32:40 | INFO | 🛑 Shutting down Vietnamese Web Novel API...
2025-07-16 08:32:40 | INFO | MetruyenScraper closed
2025-07-16 08:32:40 | INFO | ✅ AI enhancement service cleaned up
2025-07-16 08:32:40 | INFO | ✅ Services cleaned up
2025-07-16 08:32:40 | INFO | ✅ Application shutdown completed
2025-07-16 08:32:49 | INFO | MetruyenScraper initialized
2025-07-16 08:32:49 | INFO | ✅ Scraper configured
2025-07-16 08:32:49 | INFO | Initializing AI enhancement service...
2025-07-16 08:32:49 | INFO | Initialized Gemini model: gemini-2.5-flash
2025-07-16 08:32:49 | INFO | ✅ AI enhancement service initialized successfully
2025-07-16 08:32:49 | INFO | ✅ Enhancement service initialized
2025-07-16 08:32:49 | INFO | ✅ Application startup completed
2025-07-16 08:32:51 | INFO | 🔵 GET /docs - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:32:51 | INFO | ✅ GET /docs - Status: 200 - Time: 0.001s - Size: 937
2025-07-16 08:32:51 | INFO | 🔵 GET /openapi.json - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:32:51 | INFO | ✅ GET /openapi.json - Status: 200 - Time: 0.027s - Size: 55174
2025-07-16 08:33:16 | INFO | 🔵 POST /api/v1/batch-scraping/scrape-chapters - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:33:16 | INFO | ✅ POST /api/v1/batch-scraping/scrape-chapters - Status: 200 - Time: 0.007s - Size: 607
2025-07-16 08:33:42 | INFO | 🔵 GET /api/v1/stories - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:33:42 | INFO | 🔄 GET /api/v1/stories - Status: 307 - Time: 0.002s - Size: 0
2025-07-16 08:33:42 | INFO | 🔵 GET /api/v1/stories - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:33:42 | INFO | 🔄 GET /api/v1/stories - Status: 307 - Time: 0.001s - Size: 0
2025-07-16 08:33:42 | INFO | 🔵 GET /api/v1/stories/ - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:33:42 | INFO | Getting story list - page: 1, size: 12
2025-07-16 08:33:42 | INFO | ✅ GET /api/v1/stories/ - Status: 200 - Time: 0.006s - Size: 778
2025-07-16 08:33:42 | INFO | 🔵 GET /api/v1/stories/ - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:33:42 | INFO | Getting story list - page: 1, size: 12
2025-07-16 08:33:42 | INFO | ✅ GET /api/v1/stories/ - Status: 200 - Time: 0.009s - Size: 778
2025-07-16 08:33:59 | INFO | 🔵 GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:33:59 | INFO | Getting story details for: 6874cf0fd2a3c69ee3d8b22b
2025-07-16 08:33:59 | INFO | 🔵 GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b/chapters - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:33:59 | INFO | Getting chapter list for story: 6874cf0fd2a3c69ee3d8b22b
2025-07-16 08:33:59 | INFO | ✅ GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b - Status: 200 - Time: 0.003s - Size: 2088
2025-07-16 08:33:59 | INFO | 🔵 GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:33:59 | INFO | Getting story details for: 6874cf0fd2a3c69ee3d8b22b
2025-07-16 08:33:59 | INFO | ✅ GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b - Status: 200 - Time: 0.002s - Size: 2088
2025-07-16 08:33:59 | INFO | ✅ GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b/chapters - Status: 200 - Time: 0.014s - Size: 31157
2025-07-16 08:33:59 | INFO | 🔵 GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b/chapters - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:33:59 | INFO | Getting chapter list for story: 6874cf0fd2a3c69ee3d8b22b
2025-07-16 08:33:59 | INFO | ✅ GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b/chapters - Status: 200 - Time: 0.009s - Size: 31157
2025-07-16 08:38:20 | INFO | 🔵 OPTIONS /batch-scraping/scrape-chapters - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:38:20 | INFO | ✅ OPTIONS /batch-scraping/scrape-chapters - Status: 200 - Time: 0.001s - Size: 2
2025-07-16 08:38:20 | INFO | 🔵 POST /batch-scraping/scrape-chapters - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:38:20 | INFO | ⚠️ POST /batch-scraping/scrape-chapters - Status: 404 - Time: 0.001s - Size: 74
2025-07-16 08:39:18 | INFO | 🔵 GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b/chapters - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:39:18 | INFO | Getting chapter list for story: 6874cf0fd2a3c69ee3d8b22b
2025-07-16 08:39:18 | INFO | ✅ GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b/chapters - Status: 200 - Time: 0.011s - Size: 31157
2025-07-16 08:39:24 | INFO | 🔵 OPTIONS /api/v1/batch-scraping/scrape-chapters - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:39:24 | INFO | ✅ OPTIONS /api/v1/batch-scraping/scrape-chapters - Status: 200 - Time: 0.002s - Size: 2
2025-07-16 08:39:24 | INFO | 🔵 POST /api/v1/batch-scraping/scrape-chapters - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:39:24 | INFO | ⚠️ POST /api/v1/batch-scraping/scrape-chapters - Status: 422 - Time: 0.003s - Size: 227
2025-07-16 08:39:50 | INFO | 🔵 POST /api/v1/batch-scraping/scrape-chapters - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:39:50 | INFO | ✅ POST /api/v1/batch-scraping/scrape-chapters - Status: 200 - Time: 0.002s - Size: 279
2025-07-16 08:39:52 | INFO | 🔵 OPTIONS /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:39:52 | INFO | ✅ OPTIONS /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 200 - Time: 0.001s - Size: 2
2025-07-16 08:39:52 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:39:52 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.001s - Size: 78
2025-07-16 08:39:54 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:39:54 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.001s - Size: 78
2025-07-16 08:39:56 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:39:56 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.001s - Size: 78
2025-07-16 08:39:58 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:39:58 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.002s - Size: 78
2025-07-16 08:40:00 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:00 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.001s - Size: 78
2025-07-16 08:40:02 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:02 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.002s - Size: 78
2025-07-16 08:40:04 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:04 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.001s - Size: 78
2025-07-16 08:40:06 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:06 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.001s - Size: 78
2025-07-16 08:40:08 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:08 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.002s - Size: 78
2025-07-16 08:40:10 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:10 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.002s - Size: 78
2025-07-16 08:40:12 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:12 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.001s - Size: 78
2025-07-16 08:40:14 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:14 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.001s - Size: 78
2025-07-16 08:40:16 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:16 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.004s - Size: 78
2025-07-16 08:40:18 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:18 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.002s - Size: 78
2025-07-16 08:40:20 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:20 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.002s - Size: 78
2025-07-16 08:40:22 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:22 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.002s - Size: 78
2025-07-16 08:40:24 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:24 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.003s - Size: 78
2025-07-16 08:40:26 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:26 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.003s - Size: 78
2025-07-16 08:40:28 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:28 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.003s - Size: 78
2025-07-16 08:40:30 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:30 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.001s - Size: 78
2025-07-16 08:40:32 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:32 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.003s - Size: 78
2025-07-16 08:40:34 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:34 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.003s - Size: 78
2025-07-16 08:40:36 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:36 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.003s - Size: 78
2025-07-16 08:40:38 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:38 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.001s - Size: 78
2025-07-16 08:40:40 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:40 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.003s - Size: 78
2025-07-16 08:40:42 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:42 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.003s - Size: 78
2025-07-16 08:40:44 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:44 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.002s - Size: 78
2025-07-16 08:40:46 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:46 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.001s - Size: 78
2025-07-16 08:40:48 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:48 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.001s - Size: 78
2025-07-16 08:40:50 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:50 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.002s - Size: 78
2025-07-16 08:40:52 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:52 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.002s - Size: 78
2025-07-16 08:40:54 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:54 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.001s - Size: 78
2025-07-16 08:40:56 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:56 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.002s - Size: 78
2025-07-16 08:40:58 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:40:58 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.002s - Size: 78
2025-07-16 08:41:00 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:41:00 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.001s - Size: 78
2025-07-16 08:41:02 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:41:02 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.001s - Size: 78
2025-07-16 08:41:04 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:41:04 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.002s - Size: 78
2025-07-16 08:41:06 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:41:06 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.001s - Size: 78
2025-07-16 08:41:06 | INFO | 🔵 OPTIONS /api/v1/batch-scraping/jobs/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:41:06 | INFO | ✅ OPTIONS /api/v1/batch-scraping/jobs/batch_20250716_013950_4 - Status: 200 - Time: 0.002s - Size: 2
2025-07-16 08:41:06 | INFO | 🔵 DELETE /api/v1/batch-scraping/jobs/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:41:06 | INFO | ⚠️ DELETE /api/v1/batch-scraping/jobs/batch_20250716_013950_4 - Status: 404 - Time: 0.001s - Size: 74
2025-07-16 08:41:08 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:41:08 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.003s - Size: 78
2025-07-16 08:41:10 | INFO | 🔵 GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:41:10 | INFO | ⚠️ GET /api/v1/batch-scraping/progress/batch_20250716_013950_4 - Status: 404 - Time: 0.003s - Size: 78
2025-07-16 08:41:13 | INFO | 🔵 GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:41:13 | INFO | Getting story details for: 6874cf0fd2a3c69ee3d8b22b
2025-07-16 08:41:13 | INFO | ✅ GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b - Status: 200 - Time: 0.004s - Size: 2088
2025-07-16 08:41:13 | INFO | 🔵 GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b/chapters - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:41:13 | INFO | Getting chapter list for story: 6874cf0fd2a3c69ee3d8b22b
2025-07-16 08:41:13 | INFO | ✅ GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b/chapters - Status: 200 - Time: 0.014s - Size: 31157
2025-07-16 08:41:13 | INFO | 🔵 GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:41:13 | INFO | Getting story details for: 6874cf0fd2a3c69ee3d8b22b
2025-07-16 08:41:13 | INFO | ✅ GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b - Status: 200 - Time: 0.005s - Size: 2088
2025-07-16 08:41:13 | INFO | 🔵 GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b/chapters - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:41:13 | INFO | Getting chapter list for story: 6874cf0fd2a3c69ee3d8b22b
2025-07-16 08:41:13 | INFO | ✅ GET /api/v1/stories/6874cf0fd2a3c69ee3d8b22b/chapters - Status: 200 - Time: 0.022s - Size: 31157
2025-07-16 08:48:35 | INFO | 🔵 GET /docs - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:48:35 | INFO | ✅ GET /docs - Status: 200 - Time: 0.001s - Size: 937
2025-07-16 08:48:35 | INFO | 🔵 GET /openapi.json - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-16 08:48:35 | INFO | ✅ GET /openapi.json - Status: 200 - Time: 0.001s - Size: 55174
2025-07-16 08:48:40 | INFO | 🛑 Shutting down Vietnamese Web Novel API...
2025-07-16 08:48:40 | INFO | MetruyenScraper closed
2025-07-16 08:48:40 | INFO | ✅ AI enhancement service cleaned up
2025-07-16 08:48:40 | INFO | ✅ Services cleaned up
2025-07-16 08:48:40 | INFO | ✅ Application shutdown completed
