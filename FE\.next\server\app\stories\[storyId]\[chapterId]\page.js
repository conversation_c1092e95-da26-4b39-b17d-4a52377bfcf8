/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/stories/[storyId]/[chapterId]/page";
exports.ids = ["app/stories/[storyId]/[chapterId]/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fstories%2F%5BstoryId%5D%2F%5BchapterId%5D%2Fpage&page=%2Fstories%2F%5BstoryId%5D%2F%5BchapterId%5D%2Fpage&appPaths=%2Fstories%2F%5BstoryId%5D%2F%5BchapterId%5D%2Fpage&pagePath=private-next-app-dir%2Fstories%2F%5BstoryId%5D%2F%5BchapterId%5D%2Fpage.tsx&appDir=D%3A%5CPersonal%20Projects%5CVibe%5CWebtruyen%5CFE%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CPersonal%20Projects%5CVibe%5CWebtruyen%5CFE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fstories%2F%5BstoryId%5D%2F%5BchapterId%5D%2Fpage&page=%2Fstories%2F%5BstoryId%5D%2F%5BchapterId%5D%2Fpage&appPaths=%2Fstories%2F%5BstoryId%5D%2F%5BchapterId%5D%2Fpage&pagePath=private-next-app-dir%2Fstories%2F%5BstoryId%5D%2F%5BchapterId%5D%2Fpage.tsx&appDir=D%3A%5CPersonal%20Projects%5CVibe%5CWebtruyen%5CFE%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CPersonal%20Projects%5CVibe%5CWebtruyen%5CFE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'stories',\n        {\n        children: [\n        '[storyId]',\n        {\n        children: [\n        '[chapterId]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/stories/[storyId]/[chapterId]/page.tsx */ \"(rsc)/./src/app/stories/[storyId]/[chapterId]/page.tsx\")), \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\stories\\\\[storyId]\\\\[chapterId]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\layout.tsx\"],\n'loading': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/loading.tsx */ \"(rsc)/./src/app/loading.tsx\")), \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\loading.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\")), \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\stories\\\\[storyId]\\\\[chapterId]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/stories/[storyId]/[chapterId]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/stories/[storyId]/[chapterId]/page\",\n        pathname: \"/stories/[storyId]/[chapterId]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fstories%2F%5BstoryId%5D%2F%5BchapterId%5D%2Fpage&page=%2Fstories%2F%5BstoryId%5D%2F%5BchapterId%5D%2Fpage&appPaths=%2Fstories%2F%5BstoryId%5D%2F%5BchapterId%5D%2Fpage&pagePath=private-next-app-dir%2Fstories%2F%5BstoryId%5D%2F%5BchapterId%5D%2Fpage.tsx&appDir=D%3A%5CPersonal%20Projects%5CVibe%5CWebtruyen%5CFE%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CPersonal%20Projects%5CVibe%5CWebtruyen%5CFE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQZXJzb25hbCUyMFByb2plY3RzJTVDJTVDVmliZSU1QyU1Q1dlYnRydXllbiU1QyU1Q0ZFJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNsaW5rLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyKiUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDUGVyc29uYWwlMjBQcm9qZWN0cyU1QyU1Q1ZpYmUlNUMlNUNXZWJ0cnV5ZW4lNUMlNUNGRSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDUGVyc29uYWwlMjBQcm9qZWN0cyU1QyU1Q1ZpYmUlNUMlNUNXZWJ0cnV5ZW4lNUMlNUNGRSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBMEgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mZS8/NWZlMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFBlcnNvbmFsIFByb2plY3RzXFxcXFZpYmVcXFxcV2VidHJ1eWVuXFxcXEZFXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGxpbmsuanNcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQZXJzb25hbCUyMFByb2plY3RzJTVDJTVDVmliZSU1QyU1Q1dlYnRydXllbiU1QyU1Q0ZFJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNsaW5rLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyKiUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ01BQTBIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmUvPzI4MWIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQZXJzb25hbCBQcm9qZWN0c1xcXFxWaWJlXFxcXFdlYnRydXllblxcXFxGRVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxsaW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Csrc%5C%5Ccomponents%5C%5Creader%5C%5CChapterReaderWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Csrc%5C%5Ccomponents%5C%5Creader%5C%5CChapterReaderWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/reader/ChapterReaderWrapper.tsx */ \"(ssr)/./src/components/reader/ChapterReaderWrapper.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQZXJzb25hbCUyMFByb2plY3RzJTVDJTVDVmliZSU1QyU1Q1dlYnRydXllbiU1QyU1Q0ZFJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q3JlYWRlciU1QyU1Q0NoYXB0ZXJSZWFkZXJXcmFwcGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRNQUErSiIsInNvdXJjZXMiOlsid2VicGFjazovL2ZlLz9mNTkxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkQ6XFxcXFBlcnNvbmFsIFByb2plY3RzXFxcXFZpYmVcXFxcV2VidHJ1eWVuXFxcXEZFXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHJlYWRlclxcXFxDaGFwdGVyUmVhZGVyV3JhcHBlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Csrc%5C%5Ccomponents%5C%5Creader%5C%5CChapterReaderWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/components/reader/ChapterNavigation.tsx":
/*!*****************************************************!*\
  !*** ./src/components/reader/ChapterNavigation.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst ChapterNavigation = ({ storyId, currentChapter, previousChapter, nextChapter, allChapters })=>{\n    const [isDropdownOpen, setIsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const handleChapterSelect = (chapterNumber)=>{\n        router.push(`/stories/${storyId}/${chapterNumber}`);\n        setIsDropdownOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-zinc-800/50 rounded-lg p-4 mb-6 border border-zinc-700/50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row items-center justify-between gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: previousChapter ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: `/stories/${storyId}/${previousChapter.chapter_number}`,\n                            className: \"inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 mr-2\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M15 19l-7-7 7-7\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Chương trước\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            disabled: true,\n                            className: \"inline-flex items-center px-4 py-2 bg-zinc-700 text-gray-400 rounded-md cursor-not-allowed\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 mr-2\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M15 19l-7-7 7-7\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Chương trước\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsDropdownOpen(!isDropdownOpen),\n                                className: \"inline-flex items-center px-4 py-2 bg-zinc-700 hover:bg-zinc-600 text-white rounded-md transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Chương \",\n                                            currentChapter.chapter_number\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 ml-2\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M19 9l-7 7-7-7\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, undefined),\n                            isDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-full left-1/2 transform -translate-x-1/2 mt-2 w-64 bg-zinc-800 border border-zinc-700 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto\",\n                                children: allChapters.map((chapter)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleChapterSelect(chapter.chapter_number),\n                                        className: `w-full text-left px-4 py-2 hover:bg-zinc-700 transition-colors ${chapter.id === currentChapter.id ? \"bg-indigo-600 text-white\" : \"text-gray-300\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Chương \",\n                                                            chapter.chapter_number\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    chapter.id === currentChapter.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                                                            lineNumber: 87,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            chapter.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-400 truncate mt-1\",\n                                                children: chapter.title\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, chapter.id, true, {\n                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex justify-end\",\n                        children: nextChapter ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: `/stories/${storyId}/${nextChapter.chapter_number}`,\n                            className: \"inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md transition-colors\",\n                            children: [\n                                \"Chương sau\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 ml-2\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M9 5l7 7-7 7\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            disabled: true,\n                            className: \"inline-flex items-center px-4 py-2 bg-zinc-700 text-gray-400 rounded-md cursor-not-allowed\",\n                            children: [\n                                \"Chương sau\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 ml-2\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M9 5l7 7-7 7\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    href: `/stories/${storyId}`,\n                    className: \"inline-flex items-center px-3 py-1 text-sm bg-zinc-700 hover:bg-zinc-600 text-gray-300 hover:text-white rounded transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 mr-1\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M10 19l-7-7m0 0l7-7m-7 7h18\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, undefined),\n                        \"Về trang truyện\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, undefined),\n            isDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40\",\n                onClick: ()=>setIsDropdownOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n                lineNumber: 143,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterNavigation.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChapterNavigation);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/reader/ChapterNavigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/reader/ChapterReaderWrapper.tsx":
/*!********************************************************!*\
  !*** ./src/components/reader/ChapterReaderWrapper.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useChaptersCache__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useChaptersCache */ \"(ssr)/./src/hooks/useChaptersCache.ts\");\n/* harmony import */ var _ReaderView__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ReaderView */ \"(ssr)/./src/components/reader/ReaderView.tsx\");\n/* harmony import */ var _ChapterNavigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ChapterNavigation */ \"(ssr)/./src/components/reader/ChapterNavigation.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst ChapterReaderWrapper = ({ story, chapter, storyId })=>{\n    const { chapters: allChapters, loading, error, loadAllChapters } = (0,_hooks_useChaptersCache__WEBPACK_IMPORTED_MODULE_2__.useChaptersCache)(storyId);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Load chapters for navigation using cache\n        loadAllChapters(5); // Load up to 5 pages (500 chapters)\n    }, [\n        storyId,\n        loadAllChapters\n    ]);\n    // Find navigation chapters\n    const currentChapterIndex = allChapters.findIndex((ch)=>ch.chapter_number === chapter.chapter_number);\n    const previousChapter = currentChapterIndex > 0 ? allChapters[currentChapterIndex - 1] : null;\n    const nextChapter = currentChapterIndex < allChapters.length - 1 ? allChapters[currentChapterIndex + 1] : null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-zinc-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-white mb-2\",\n                            children: story.title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterReaderWrapper.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl text-zinc-300 mb-4\",\n                            children: [\n                                \"Chương \",\n                                chapter.chapter_number,\n                                \": \",\n                                chapter.title\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterReaderWrapper.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterReaderWrapper.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, undefined),\n                !loading && allChapters.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChapterNavigation__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    storyId: storyId,\n                    currentChapter: chapter,\n                    previousChapter: previousChapter,\n                    nextChapter: nextChapter,\n                    allChapters: allChapters\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterReaderWrapper.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 11\n                }, undefined),\n                loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-zinc-800/50 rounded-lg p-4 mb-6 border border-zinc-700/50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-zinc-400\",\n                        children: \"Đang tải điều hướng chương...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterReaderWrapper.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterReaderWrapper.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, undefined),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-900/20 border border-red-700/50 rounded-lg p-4 mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-400 text-center\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterReaderWrapper.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterReaderWrapper.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReaderView__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    chapter: chapter\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterReaderWrapper.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, undefined),\n                !loading && allChapters.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChapterNavigation__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        storyId: storyId,\n                        currentChapter: chapter,\n                        previousChapter: previousChapter,\n                        nextChapter: nextChapter,\n                        allChapters: allChapters\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterReaderWrapper.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterReaderWrapper.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterReaderWrapper.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ChapterReaderWrapper.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChapterReaderWrapper);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/reader/ChapterReaderWrapper.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/reader/ReaderView.tsx":
/*!**********************************************!*\
  !*** ./src/components/reader/ReaderView.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst ReaderView = ({ chapter })=>{\n    const [fontSize, setFontSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(16);\n    const [lineHeight, setLineHeight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.8);\n    const [isDarkMode, setIsDarkMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const formatChapterContent = (content)=>{\n        if (!content) return \"Nội dung chưa c\\xf3 sẵn\";\n        // Clean up the content and format paragraphs for Vietnamese text\n        let formattedContent = content// Normalize line endings\n        .replace(/\\r\\n/g, \"\\n\").replace(/\\r/g, \"\\n\")// Remove excessive whitespace but preserve intentional spacing\n        .replace(/[ \\t]+/g, \" \") // Multiple spaces/tabs to single space\n        .replace(/\\n[ \\t]+/g, \"\\n\") // Remove leading whitespace on lines\n        .replace(/[ \\t]+\\n/g, \"\\n\") // Remove trailing whitespace on lines\n        // Handle common Vietnamese punctuation and spacing\n        .replace(/\\s*([.!?])\\s*/g, \"$1 \") // Normalize punctuation spacing\n        .replace(/\\s*([,;:])\\s*/g, \"$1 \") // Normalize comma/semicolon spacing\n        .replace(/\\s*([\"\"''„\"])\\s*/g, \" $1\") // Handle Vietnamese quotes\n        .replace(/([\"\"''„\"])\\s*/g, \"$1 \") // Handle closing quotes\n        // Convert line breaks to paragraph breaks\n        .replace(/\\n\\s*\\n\\s*/g, \"\\n\\n\") // Normalize paragraph breaks\n        .split(\"\\n\\n\") // Split into paragraphs\n        .map((paragraph)=>paragraph.trim()) // Trim each paragraph\n        .filter((paragraph)=>paragraph.length > 0) // Remove empty paragraphs\n        .map((paragraph)=>{\n            // Add proper paragraph styling with Vietnamese text considerations\n            const isDialogue = paragraph.match(/^[\"\"''„\"]/); // Check if it's dialogue\n            const isTitle = paragraph.length < 100 && paragraph.match(/^[A-ZÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ]/);\n            let style = \"margin-bottom: 1.5em; line-height: 1.8;\";\n            if (isDialogue) {\n                style += \" text-indent: 1em; font-style: italic;\"; // Less indent for dialogue\n            } else if (isTitle) {\n                style += \" text-align: center; font-weight: bold; margin-bottom: 2em; text-indent: 0;\"; // Center titles\n            } else {\n                style += \" text-indent: 2em;\"; // Standard paragraph indent\n            }\n            return `<p style=\"${style}\">${paragraph}</p>`;\n        }).join(\"\");\n        return formattedContent || '<p style=\"text-align: center; color: #9ca3af;\">Nội dung chưa c\\xf3 sẵn</p>';\n    };\n    const increaseFontSize = ()=>{\n        if (fontSize < 24) setFontSize(fontSize + 2);\n    };\n    const decreaseFontSize = ()=>{\n        if (fontSize > 12) setFontSize(fontSize - 2);\n    };\n    const increaseLineHeight = ()=>{\n        if (lineHeight < 2.5) setLineHeight(lineHeight + 0.2);\n    };\n    const decreaseLineHeight = ()=>{\n        if (lineHeight > 1.2) setLineHeight(lineHeight - 0.2);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"my-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-zinc-800/50 rounded-lg p-4 mb-6 border border-zinc-700/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"Cỡ chữ:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: decreaseFontSize,\n                                    className: \"w-8 h-8 bg-zinc-700 hover:bg-zinc-600 rounded text-white transition-colors\",\n                                    disabled: fontSize <= 12,\n                                    children: \"A-\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-white w-8 text-center\",\n                                    children: fontSize\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: increaseFontSize,\n                                    className: \"w-8 h-8 bg-zinc-700 hover:bg-zinc-600 rounded text-white transition-colors\",\n                                    disabled: fontSize >= 24,\n                                    children: \"A+\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"Gi\\xe3n d\\xf2ng:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: decreaseLineHeight,\n                                    className: \"w-8 h-8 bg-zinc-700 hover:bg-zinc-600 rounded text-white transition-colors\",\n                                    disabled: lineHeight <= 1.2,\n                                    children: \"-\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-white w-12 text-center\",\n                                    children: lineHeight.toFixed(1)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: increaseLineHeight,\n                                    className: \"w-8 h-8 bg-zinc-700 hover:bg-zinc-600 rounded text-white transition-colors\",\n                                    disabled: lineHeight >= 2.5,\n                                    children: \"+\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsDarkMode(!isDarkMode),\n                            className: \"flex items-center space-x-2 px-3 py-2 bg-zinc-700 hover:bg-zinc-600 rounded text-white transition-colors\",\n                            children: isDarkMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"S\\xe1ng\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Tối\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `rounded-lg p-6 border transition-colors ${isDarkMode ? \"bg-zinc-800/50 border-zinc-700/50 text-gray-100\" : \"bg-white border-gray-200 text-gray-900\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"prose prose-lg max-w-none\",\n                    style: {\n                        fontSize: `${fontSize}px`,\n                        lineHeight: lineHeight,\n                        color: isDarkMode ? \"#f3f4f6\" : \"#111827\",\n                        fontFamily: '\"Inter\", \"Segoe UI\", \"Roboto\", \"Helvetica Neue\", Arial, sans-serif'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chapter-content vietnamese-text\",\n                        style: {\n                            wordBreak: \"break-word\",\n                            textAlign: \"justify\",\n                            textJustify: \"inter-word\",\n                            hyphens: \"auto\",\n                            WebkitHyphens: \"auto\",\n                            MozHyphens: \"auto\",\n                            msHyphens: \"auto\",\n                            // Better Vietnamese text rendering\n                            fontFeatureSettings: '\"kern\" 1, \"liga\" 1, \"calt\" 1',\n                            WebkitFontFeatureSettings: '\"kern\" 1, \"liga\" 1, \"calt\" 1',\n                            // Improve readability\n                            letterSpacing: \"0.01em\",\n                            wordSpacing: \"0.05em\",\n                            // Smooth text rendering\n                            WebkitFontSmoothing: \"antialiased\",\n                            MozOsxFontSmoothing: \"grayscale\",\n                            textRendering: \"optimizeLegibility\"\n                        },\n                        dangerouslySetInnerHTML: {\n                            __html: formatChapterContent(chapter.enhanced_content || chapter.original_content || \"Nội dung chưa c\\xf3 sẵn\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 text-center text-sm text-gray-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"Chương \",\n                            chapter.chapter_number,\n                            \" • \",\n                            chapter.word_count?.toLocaleString() || 0,\n                            \" từ\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"Cập nhật: \",\n                            new Date(chapter.updated_at).toLocaleDateString(\"vi-VN\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center gap-2 mt-2\",\n                        children: [\n                            chapter.is_scraped && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-green-400 bg-green-900/30 px-2 py-1 rounded\",\n                                children: \"Đ\\xe3 c\\xe0o\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, undefined),\n                            chapter.is_enhanced && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-blue-400 bg-blue-900/30 px-2 py-1 rounded\",\n                                children: \"Đ\\xe3 n\\xe2ng cấp\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\reader\\\\ReaderView.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReaderView);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/reader/ReaderView.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useChaptersCache.ts":
/*!***************************************!*\
  !*** ./src/hooks/useChaptersCache.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useChaptersCache: () => (/* binding */ useChaptersCache)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _services_storyService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/storyService */ \"(ssr)/./src/services/storyService.ts\");\n/* __next_internal_client_entry_do_not_use__ useChaptersCache,default auto */ \n\nconst chaptersCache = new Map();\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutes\nconst useChaptersCache = (storyId)=>{\n    const [chapters, setChapters] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n    const [totalChapters, setTotalChapters] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(50);\n    const isCacheValid = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((entry)=>{\n        return Date.now() - entry.lastFetched < CACHE_DURATION;\n    }, []);\n    const loadChapters = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (page = 1, pageSizeParam = 50, forceRefresh = false, filters)=>{\n        const filterKey = filters ? `${filters.enhanced_only || false}-${filters.scraped_only || false}` : \"none\";\n        const cacheKey = `${storyId}-${page}-${pageSizeParam}-${filterKey}`;\n        const cachedEntry = chaptersCache.get(cacheKey);\n        // Return cached data if valid and not forcing refresh\n        if (!forceRefresh && cachedEntry && isCacheValid(cachedEntry)) {\n            setChapters(cachedEntry.chapters);\n            setTotalChapters(cachedEntry.totalChapters);\n            setCurrentPage(page);\n            setPageSize(pageSizeParam);\n            setTotalPages(Math.ceil(cachedEntry.totalChapters / pageSizeParam));\n            return cachedEntry;\n        }\n        try {\n            setLoading(true);\n            setError(\"\");\n            const response = await (0,_services_storyService__WEBPACK_IMPORTED_MODULE_1__.fetchChaptersByStoryId)(storyId, page, pageSizeParam, filters);\n            const newEntry = {\n                chapters: response.data,\n                totalChapters: response.pagination.total_items,\n                lastFetched: Date.now(),\n                isComplete: !response.pagination.has_next\n            };\n            // Cache the result\n            chaptersCache.set(cacheKey, newEntry);\n            setChapters(response.data);\n            setTotalChapters(response.pagination.total_items);\n            setCurrentPage(page);\n            setPageSize(pageSizeParam);\n            setTotalPages(Math.ceil(response.pagination.total_items / pageSizeParam));\n            return newEntry;\n        } catch (err) {\n            const errorMessage = \"Kh\\xf4ng thể tải danh s\\xe1ch chương\";\n            setError(errorMessage);\n            console.error(\"Error loading chapters:\", err);\n            throw err;\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        storyId,\n        isCacheValid\n    ]);\n    const loadAllChapters = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (maxPages = 5)=>{\n        const allChaptersCacheKey = `${storyId}-all`;\n        const cachedEntry = chaptersCache.get(allChaptersCacheKey);\n        // Return cached data if valid\n        if (cachedEntry && isCacheValid(cachedEntry) && cachedEntry.isComplete) {\n            setChapters(cachedEntry.chapters);\n            setTotalChapters(cachedEntry.totalChapters);\n            return cachedEntry.chapters;\n        }\n        try {\n            setLoading(true);\n            setError(\"\");\n            // Load first page to get total count\n            const firstResponse = await (0,_services_storyService__WEBPACK_IMPORTED_MODULE_1__.fetchChaptersByStoryId)(storyId, 1, 100);\n            let allChapters = [\n                ...firstResponse.data\n            ];\n            // Load additional pages if needed\n            if (firstResponse.pagination.has_next) {\n                const totalPages = Math.ceil(firstResponse.pagination.total_items / 100);\n                const pagesToLoad = Math.min(totalPages, maxPages) - 1;\n                const promises = [];\n                for(let page = 2; page <= pagesToLoad + 1; page++){\n                    promises.push((0,_services_storyService__WEBPACK_IMPORTED_MODULE_1__.fetchChaptersByStoryId)(storyId, page, 100));\n                }\n                const additionalResponses = await Promise.all(promises);\n                const additionalChapters = additionalResponses.flatMap((res)=>res.data);\n                allChapters = [\n                    ...allChapters,\n                    ...additionalChapters\n                ];\n            }\n            const newEntry = {\n                chapters: allChapters,\n                totalChapters: firstResponse.pagination.total_items,\n                lastFetched: Date.now(),\n                isComplete: allChapters.length >= firstResponse.pagination.total_items\n            };\n            // Cache the result\n            chaptersCache.set(allChaptersCacheKey, newEntry);\n            setChapters(allChapters);\n            setTotalChapters(firstResponse.pagination.total_items);\n            return allChapters;\n        } catch (err) {\n            const errorMessage = \"Kh\\xf4ng thể tải danh s\\xe1ch chương\";\n            setError(errorMessage);\n            console.error(\"Error loading all chapters:\", err);\n            throw err;\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        storyId,\n        isCacheValid\n    ]);\n    const clearCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        // Clear all cache entries for this story\n        Array.from(chaptersCache.keys()).forEach((key)=>{\n            if (key.startsWith(storyId)) {\n                chaptersCache.delete(key);\n            }\n        });\n    }, [\n        storyId\n    ]);\n    return {\n        chapters,\n        loading,\n        error,\n        totalChapters,\n        currentPage,\n        totalPages,\n        pageSize,\n        loadChapters,\n        loadAllChapters,\n        clearCache\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useChaptersCache);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlQ2hhcHRlcnNDYWNoZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs4RUFFeUQ7QUFFUTtBQWNqRSxNQUFNRyxnQkFBZ0IsSUFBSUM7QUFDMUIsTUFBTUMsaUJBQWlCLElBQUksS0FBSyxNQUFNLFlBQVk7QUFFM0MsTUFBTUMsbUJBQW1CLENBQUNDO0lBQy9CLE1BQU0sQ0FBQ0MsVUFBVUMsWUFBWSxHQUFHVCwrQ0FBUUEsQ0FBWSxFQUFFO0lBQ3RELE1BQU0sQ0FBQ1UsU0FBU0MsV0FBVyxHQUFHWCwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNZLE9BQU9DLFNBQVMsR0FBR2IsK0NBQVFBLENBQVM7SUFDM0MsTUFBTSxDQUFDYyxlQUFlQyxpQkFBaUIsR0FBR2YsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDZ0IsYUFBYUMsZUFBZSxHQUFHakIsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDa0IsWUFBWUMsY0FBYyxHQUFHbkIsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDb0IsVUFBVUMsWUFBWSxHQUFHckIsK0NBQVFBLENBQUM7SUFFekMsTUFBTXNCLGVBQWVyQixrREFBV0EsQ0FBQyxDQUFDc0I7UUFDaEMsT0FBT0MsS0FBS0MsR0FBRyxLQUFLRixNQUFNRyxXQUFXLEdBQUdyQjtJQUMxQyxHQUFHLEVBQUU7SUFFTCxNQUFNc0IsZUFBZTFCLGtEQUFXQSxDQUFDLE9BQy9CMkIsT0FBZSxDQUFDLEVBQ2hCQyxnQkFBd0IsRUFBRSxFQUMxQkMsZUFBd0IsS0FBSyxFQUM3QkM7UUFFQSxNQUFNQyxZQUFZRCxVQUFVLENBQUMsRUFBRUEsUUFBUUUsYUFBYSxJQUFJLE1BQU0sQ0FBQyxFQUFFRixRQUFRRyxZQUFZLElBQUksTUFBTSxDQUFDLEdBQUc7UUFDbkcsTUFBTUMsV0FBVyxDQUFDLEVBQUU1QixRQUFRLENBQUMsRUFBRXFCLEtBQUssQ0FBQyxFQUFFQyxjQUFjLENBQUMsRUFBRUcsVUFBVSxDQUFDO1FBQ25FLE1BQU1JLGNBQWNqQyxjQUFja0MsR0FBRyxDQUFDRjtRQUV0QyxzREFBc0Q7UUFDdEQsSUFBSSxDQUFDTCxnQkFBZ0JNLGVBQWVkLGFBQWFjLGNBQWM7WUFDN0QzQixZQUFZMkIsWUFBWTVCLFFBQVE7WUFDaENPLGlCQUFpQnFCLFlBQVl0QixhQUFhO1lBQzFDRyxlQUFlVztZQUNmUCxZQUFZUTtZQUNaVixjQUFjbUIsS0FBS0MsSUFBSSxDQUFDSCxZQUFZdEIsYUFBYSxHQUFHZTtZQUNwRCxPQUFPTztRQUNUO1FBRUEsSUFBSTtZQUNGekIsV0FBVztZQUNYRSxTQUFTO1lBRVQsTUFBTTJCLFdBQVcsTUFBTXRDLDhFQUFzQkEsQ0FBQ0ssU0FBU3FCLE1BQU1DLGVBQWVFO1lBRTVFLE1BQU1VLFdBQStCO2dCQUNuQ2pDLFVBQVVnQyxTQUFTRSxJQUFJO2dCQUN2QjVCLGVBQWUwQixTQUFTRyxVQUFVLENBQUNDLFdBQVc7Z0JBQzlDbEIsYUFBYUYsS0FBS0MsR0FBRztnQkFDckJvQixZQUFZLENBQUNMLFNBQVNHLFVBQVUsQ0FBQ0csUUFBUTtZQUMzQztZQUVBLG1CQUFtQjtZQUNuQjNDLGNBQWM0QyxHQUFHLENBQUNaLFVBQVVNO1lBRTVCaEMsWUFBWStCLFNBQVNFLElBQUk7WUFDekIzQixpQkFBaUJ5QixTQUFTRyxVQUFVLENBQUNDLFdBQVc7WUFDaEQzQixlQUFlVztZQUNmUCxZQUFZUTtZQUNaVixjQUFjbUIsS0FBS0MsSUFBSSxDQUFDQyxTQUFTRyxVQUFVLENBQUNDLFdBQVcsR0FBR2Y7WUFFMUQsT0FBT1k7UUFDVCxFQUFFLE9BQU9PLEtBQUs7WUFDWixNQUFNQyxlQUFlO1lBQ3JCcEMsU0FBU29DO1lBQ1RDLFFBQVF0QyxLQUFLLENBQUMsMkJBQTJCb0M7WUFDekMsTUFBTUE7UUFDUixTQUFVO1lBQ1JyQyxXQUFXO1FBQ2I7SUFDRixHQUFHO1FBQUNKO1FBQVNlO0tBQWE7SUFFMUIsTUFBTTZCLGtCQUFrQmxELGtEQUFXQSxDQUFDLE9BQU9tRCxXQUFtQixDQUFDO1FBQzdELE1BQU1DLHNCQUFzQixDQUFDLEVBQUU5QyxRQUFRLElBQUksQ0FBQztRQUM1QyxNQUFNNkIsY0FBY2pDLGNBQWNrQyxHQUFHLENBQUNnQjtRQUV0Qyw4QkFBOEI7UUFDOUIsSUFBSWpCLGVBQWVkLGFBQWFjLGdCQUFnQkEsWUFBWVMsVUFBVSxFQUFFO1lBQ3RFcEMsWUFBWTJCLFlBQVk1QixRQUFRO1lBQ2hDTyxpQkFBaUJxQixZQUFZdEIsYUFBYTtZQUMxQyxPQUFPc0IsWUFBWTVCLFFBQVE7UUFDN0I7UUFFQSxJQUFJO1lBQ0ZHLFdBQVc7WUFDWEUsU0FBUztZQUVULHFDQUFxQztZQUNyQyxNQUFNeUMsZ0JBQWdCLE1BQU1wRCw4RUFBc0JBLENBQUNLLFNBQVMsR0FBRztZQUMvRCxJQUFJZ0QsY0FBYzttQkFBSUQsY0FBY1osSUFBSTthQUFDO1lBRXpDLGtDQUFrQztZQUNsQyxJQUFJWSxjQUFjWCxVQUFVLENBQUNHLFFBQVEsRUFBRTtnQkFDckMsTUFBTTVCLGFBQWFvQixLQUFLQyxJQUFJLENBQUNlLGNBQWNYLFVBQVUsQ0FBQ0MsV0FBVyxHQUFHO2dCQUNwRSxNQUFNWSxjQUFjbEIsS0FBS21CLEdBQUcsQ0FBQ3ZDLFlBQVlrQyxZQUFZO2dCQUVyRCxNQUFNTSxXQUFXLEVBQUU7Z0JBQ25CLElBQUssSUFBSTlCLE9BQU8sR0FBR0EsUUFBUTRCLGNBQWMsR0FBRzVCLE9BQVE7b0JBQ2xEOEIsU0FBU0MsSUFBSSxDQUFDekQsOEVBQXNCQSxDQUFDSyxTQUFTcUIsTUFBTTtnQkFDdEQ7Z0JBRUEsTUFBTWdDLHNCQUFzQixNQUFNQyxRQUFRQyxHQUFHLENBQUNKO2dCQUM5QyxNQUFNSyxxQkFBcUJILG9CQUFvQkksT0FBTyxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJdkIsSUFBSTtnQkFDdEVhLGNBQWM7dUJBQUlBO3VCQUFnQlE7aUJBQW1CO1lBQ3ZEO1lBRUEsTUFBTXRCLFdBQStCO2dCQUNuQ2pDLFVBQVUrQztnQkFDVnpDLGVBQWV3QyxjQUFjWCxVQUFVLENBQUNDLFdBQVc7Z0JBQ25EbEIsYUFBYUYsS0FBS0MsR0FBRztnQkFDckJvQixZQUFZVSxZQUFZVyxNQUFNLElBQUlaLGNBQWNYLFVBQVUsQ0FBQ0MsV0FBVztZQUN4RTtZQUVBLG1CQUFtQjtZQUNuQnpDLGNBQWM0QyxHQUFHLENBQUNNLHFCQUFxQlo7WUFFdkNoQyxZQUFZOEM7WUFDWnhDLGlCQUFpQnVDLGNBQWNYLFVBQVUsQ0FBQ0MsV0FBVztZQUVyRCxPQUFPVztRQUNULEVBQUUsT0FBT1AsS0FBSztZQUNaLE1BQU1DLGVBQWU7WUFDckJwQyxTQUFTb0M7WUFDVEMsUUFBUXRDLEtBQUssQ0FBQywrQkFBK0JvQztZQUM3QyxNQUFNQTtRQUNSLFNBQVU7WUFDUnJDLFdBQVc7UUFDYjtJQUNGLEdBQUc7UUFBQ0o7UUFBU2U7S0FBYTtJQUUxQixNQUFNNkMsYUFBYWxFLGtEQUFXQSxDQUFDO1FBQzdCLHlDQUF5QztRQUN6Q21FLE1BQU1DLElBQUksQ0FBQ2xFLGNBQWNtRSxJQUFJLElBQUlDLE9BQU8sQ0FBQ0MsQ0FBQUE7WUFDdkMsSUFBSUEsSUFBSUMsVUFBVSxDQUFDbEUsVUFBVTtnQkFDM0JKLGNBQWN1RSxNQUFNLENBQUNGO1lBQ3ZCO1FBQ0Y7SUFDRixHQUFHO1FBQUNqRTtLQUFRO0lBRVosT0FBTztRQUNMQztRQUNBRTtRQUNBRTtRQUNBRTtRQUNBRTtRQUNBRTtRQUNBRTtRQUNBTztRQUNBd0I7UUFDQWdCO0lBQ0Y7QUFDRixFQUFFO0FBRUYsaUVBQWU3RCxnQkFBZ0JBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mZS8uL3NyYy9ob29rcy91c2VDaGFwdGVyc0NhY2hlLnRzPzE2MGMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IENoYXB0ZXIsIENoYXB0ZXJzUmVzcG9uc2UgfSBmcm9tICdAL3R5cGVzL3N0b3J5JztcbmltcG9ydCB7IGZldGNoQ2hhcHRlcnNCeVN0b3J5SWQgfSBmcm9tICdAL3NlcnZpY2VzL3N0b3J5U2VydmljZSc7XG5cbmludGVyZmFjZSBDaGFwdGVyc0NhY2hlRW50cnkge1xuICBjaGFwdGVyczogQ2hhcHRlcltdO1xuICB0b3RhbENoYXB0ZXJzOiBudW1iZXI7XG4gIGxhc3RGZXRjaGVkOiBudW1iZXI7XG4gIGlzQ29tcGxldGU6IGJvb2xlYW47XG59XG5cbmludGVyZmFjZSBDaGFwdGVyRmlsdGVycyB7XG4gIGVuaGFuY2VkX29ubHk/OiBib29sZWFuO1xuICBzY3JhcGVkX29ubHk/OiBib29sZWFuO1xufVxuXG5jb25zdCBjaGFwdGVyc0NhY2hlID0gbmV3IE1hcDxzdHJpbmcsIENoYXB0ZXJzQ2FjaGVFbnRyeT4oKTtcbmNvbnN0IENBQ0hFX0RVUkFUSU9OID0gNSAqIDYwICogMTAwMDsgLy8gNSBtaW51dGVzXG5cbmV4cG9ydCBjb25zdCB1c2VDaGFwdGVyc0NhY2hlID0gKHN0b3J5SWQ6IHN0cmluZykgPT4ge1xuICBjb25zdCBbY2hhcHRlcnMsIHNldENoYXB0ZXJzXSA9IHVzZVN0YXRlPENoYXB0ZXJbXT4oW10pO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nPignJyk7XG4gIGNvbnN0IFt0b3RhbENoYXB0ZXJzLCBzZXRUb3RhbENoYXB0ZXJzXSA9IHVzZVN0YXRlKDApO1xuICBjb25zdCBbY3VycmVudFBhZ2UsIHNldEN1cnJlbnRQYWdlXSA9IHVzZVN0YXRlKDEpO1xuICBjb25zdCBbdG90YWxQYWdlcywgc2V0VG90YWxQYWdlc10gPSB1c2VTdGF0ZSgwKTtcbiAgY29uc3QgW3BhZ2VTaXplLCBzZXRQYWdlU2l6ZV0gPSB1c2VTdGF0ZSg1MCk7XG5cbiAgY29uc3QgaXNDYWNoZVZhbGlkID0gdXNlQ2FsbGJhY2soKGVudHJ5OiBDaGFwdGVyc0NhY2hlRW50cnkpID0+IHtcbiAgICByZXR1cm4gRGF0ZS5ub3coKSAtIGVudHJ5Lmxhc3RGZXRjaGVkIDwgQ0FDSEVfRFVSQVRJT047XG4gIH0sIFtdKTtcblxuICBjb25zdCBsb2FkQ2hhcHRlcnMgPSB1c2VDYWxsYmFjayhhc3luYyAoXG4gICAgcGFnZTogbnVtYmVyID0gMSxcbiAgICBwYWdlU2l6ZVBhcmFtOiBudW1iZXIgPSA1MCxcbiAgICBmb3JjZVJlZnJlc2g6IGJvb2xlYW4gPSBmYWxzZSxcbiAgICBmaWx0ZXJzPzogQ2hhcHRlckZpbHRlcnNcbiAgKSA9PiB7XG4gICAgY29uc3QgZmlsdGVyS2V5ID0gZmlsdGVycyA/IGAke2ZpbHRlcnMuZW5oYW5jZWRfb25seSB8fCBmYWxzZX0tJHtmaWx0ZXJzLnNjcmFwZWRfb25seSB8fCBmYWxzZX1gIDogJ25vbmUnO1xuICAgIGNvbnN0IGNhY2hlS2V5ID0gYCR7c3RvcnlJZH0tJHtwYWdlfS0ke3BhZ2VTaXplUGFyYW19LSR7ZmlsdGVyS2V5fWA7XG4gICAgY29uc3QgY2FjaGVkRW50cnkgPSBjaGFwdGVyc0NhY2hlLmdldChjYWNoZUtleSk7XG5cbiAgICAvLyBSZXR1cm4gY2FjaGVkIGRhdGEgaWYgdmFsaWQgYW5kIG5vdCBmb3JjaW5nIHJlZnJlc2hcbiAgICBpZiAoIWZvcmNlUmVmcmVzaCAmJiBjYWNoZWRFbnRyeSAmJiBpc0NhY2hlVmFsaWQoY2FjaGVkRW50cnkpKSB7XG4gICAgICBzZXRDaGFwdGVycyhjYWNoZWRFbnRyeS5jaGFwdGVycyk7XG4gICAgICBzZXRUb3RhbENoYXB0ZXJzKGNhY2hlZEVudHJ5LnRvdGFsQ2hhcHRlcnMpO1xuICAgICAgc2V0Q3VycmVudFBhZ2UocGFnZSk7XG4gICAgICBzZXRQYWdlU2l6ZShwYWdlU2l6ZVBhcmFtKTtcbiAgICAgIHNldFRvdGFsUGFnZXMoTWF0aC5jZWlsKGNhY2hlZEVudHJ5LnRvdGFsQ2hhcHRlcnMgLyBwYWdlU2l6ZVBhcmFtKSk7XG4gICAgICByZXR1cm4gY2FjaGVkRW50cnk7XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICBzZXRFcnJvcignJyk7XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2hDaGFwdGVyc0J5U3RvcnlJZChzdG9yeUlkLCBwYWdlLCBwYWdlU2l6ZVBhcmFtLCBmaWx0ZXJzKTtcbiAgICAgIFxuICAgICAgY29uc3QgbmV3RW50cnk6IENoYXB0ZXJzQ2FjaGVFbnRyeSA9IHtcbiAgICAgICAgY2hhcHRlcnM6IHJlc3BvbnNlLmRhdGEsXG4gICAgICAgIHRvdGFsQ2hhcHRlcnM6IHJlc3BvbnNlLnBhZ2luYXRpb24udG90YWxfaXRlbXMsXG4gICAgICAgIGxhc3RGZXRjaGVkOiBEYXRlLm5vdygpLFxuICAgICAgICBpc0NvbXBsZXRlOiAhcmVzcG9uc2UucGFnaW5hdGlvbi5oYXNfbmV4dFxuICAgICAgfTtcblxuICAgICAgLy8gQ2FjaGUgdGhlIHJlc3VsdFxuICAgICAgY2hhcHRlcnNDYWNoZS5zZXQoY2FjaGVLZXksIG5ld0VudHJ5KTtcbiAgICAgIFxuICAgICAgc2V0Q2hhcHRlcnMocmVzcG9uc2UuZGF0YSk7XG4gICAgICBzZXRUb3RhbENoYXB0ZXJzKHJlc3BvbnNlLnBhZ2luYXRpb24udG90YWxfaXRlbXMpO1xuICAgICAgc2V0Q3VycmVudFBhZ2UocGFnZSk7XG4gICAgICBzZXRQYWdlU2l6ZShwYWdlU2l6ZVBhcmFtKTtcbiAgICAgIHNldFRvdGFsUGFnZXMoTWF0aC5jZWlsKHJlc3BvbnNlLnBhZ2luYXRpb24udG90YWxfaXRlbXMgLyBwYWdlU2l6ZVBhcmFtKSk7XG4gICAgICBcbiAgICAgIHJldHVybiBuZXdFbnRyeTtcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9ICdLaMO0bmcgdGjhu4MgdOG6o2kgZGFuaCBzw6FjaCBjaMawxqFuZyc7XG4gICAgICBzZXRFcnJvcihlcnJvck1lc3NhZ2UpO1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBjaGFwdGVyczonLCBlcnIpO1xuICAgICAgdGhyb3cgZXJyO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH0sIFtzdG9yeUlkLCBpc0NhY2hlVmFsaWRdKTtcblxuICBjb25zdCBsb2FkQWxsQ2hhcHRlcnMgPSB1c2VDYWxsYmFjayhhc3luYyAobWF4UGFnZXM6IG51bWJlciA9IDUpID0+IHtcbiAgICBjb25zdCBhbGxDaGFwdGVyc0NhY2hlS2V5ID0gYCR7c3RvcnlJZH0tYWxsYDtcbiAgICBjb25zdCBjYWNoZWRFbnRyeSA9IGNoYXB0ZXJzQ2FjaGUuZ2V0KGFsbENoYXB0ZXJzQ2FjaGVLZXkpO1xuXG4gICAgLy8gUmV0dXJuIGNhY2hlZCBkYXRhIGlmIHZhbGlkXG4gICAgaWYgKGNhY2hlZEVudHJ5ICYmIGlzQ2FjaGVWYWxpZChjYWNoZWRFbnRyeSkgJiYgY2FjaGVkRW50cnkuaXNDb21wbGV0ZSkge1xuICAgICAgc2V0Q2hhcHRlcnMoY2FjaGVkRW50cnkuY2hhcHRlcnMpO1xuICAgICAgc2V0VG90YWxDaGFwdGVycyhjYWNoZWRFbnRyeS50b3RhbENoYXB0ZXJzKTtcbiAgICAgIHJldHVybiBjYWNoZWRFbnRyeS5jaGFwdGVycztcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIHNldEVycm9yKCcnKTtcbiAgICAgIFxuICAgICAgLy8gTG9hZCBmaXJzdCBwYWdlIHRvIGdldCB0b3RhbCBjb3VudFxuICAgICAgY29uc3QgZmlyc3RSZXNwb25zZSA9IGF3YWl0IGZldGNoQ2hhcHRlcnNCeVN0b3J5SWQoc3RvcnlJZCwgMSwgMTAwKTtcbiAgICAgIGxldCBhbGxDaGFwdGVycyA9IFsuLi5maXJzdFJlc3BvbnNlLmRhdGFdO1xuICAgICAgXG4gICAgICAvLyBMb2FkIGFkZGl0aW9uYWwgcGFnZXMgaWYgbmVlZGVkXG4gICAgICBpZiAoZmlyc3RSZXNwb25zZS5wYWdpbmF0aW9uLmhhc19uZXh0KSB7XG4gICAgICAgIGNvbnN0IHRvdGFsUGFnZXMgPSBNYXRoLmNlaWwoZmlyc3RSZXNwb25zZS5wYWdpbmF0aW9uLnRvdGFsX2l0ZW1zIC8gMTAwKTtcbiAgICAgICAgY29uc3QgcGFnZXNUb0xvYWQgPSBNYXRoLm1pbih0b3RhbFBhZ2VzLCBtYXhQYWdlcykgLSAxO1xuICAgICAgICBcbiAgICAgICAgY29uc3QgcHJvbWlzZXMgPSBbXTtcbiAgICAgICAgZm9yIChsZXQgcGFnZSA9IDI7IHBhZ2UgPD0gcGFnZXNUb0xvYWQgKyAxOyBwYWdlKyspIHtcbiAgICAgICAgICBwcm9taXNlcy5wdXNoKGZldGNoQ2hhcHRlcnNCeVN0b3J5SWQoc3RvcnlJZCwgcGFnZSwgMTAwKSk7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIGNvbnN0IGFkZGl0aW9uYWxSZXNwb25zZXMgPSBhd2FpdCBQcm9taXNlLmFsbChwcm9taXNlcyk7XG4gICAgICAgIGNvbnN0IGFkZGl0aW9uYWxDaGFwdGVycyA9IGFkZGl0aW9uYWxSZXNwb25zZXMuZmxhdE1hcChyZXMgPT4gcmVzLmRhdGEpO1xuICAgICAgICBhbGxDaGFwdGVycyA9IFsuLi5hbGxDaGFwdGVycywgLi4uYWRkaXRpb25hbENoYXB0ZXJzXTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgbmV3RW50cnk6IENoYXB0ZXJzQ2FjaGVFbnRyeSA9IHtcbiAgICAgICAgY2hhcHRlcnM6IGFsbENoYXB0ZXJzLFxuICAgICAgICB0b3RhbENoYXB0ZXJzOiBmaXJzdFJlc3BvbnNlLnBhZ2luYXRpb24udG90YWxfaXRlbXMsXG4gICAgICAgIGxhc3RGZXRjaGVkOiBEYXRlLm5vdygpLFxuICAgICAgICBpc0NvbXBsZXRlOiBhbGxDaGFwdGVycy5sZW5ndGggPj0gZmlyc3RSZXNwb25zZS5wYWdpbmF0aW9uLnRvdGFsX2l0ZW1zXG4gICAgICB9O1xuXG4gICAgICAvLyBDYWNoZSB0aGUgcmVzdWx0XG4gICAgICBjaGFwdGVyc0NhY2hlLnNldChhbGxDaGFwdGVyc0NhY2hlS2V5LCBuZXdFbnRyeSk7XG4gICAgICBcbiAgICAgIHNldENoYXB0ZXJzKGFsbENoYXB0ZXJzKTtcbiAgICAgIHNldFRvdGFsQ2hhcHRlcnMoZmlyc3RSZXNwb25zZS5wYWdpbmF0aW9uLnRvdGFsX2l0ZW1zKTtcbiAgICAgIFxuICAgICAgcmV0dXJuIGFsbENoYXB0ZXJzO1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gJ0tow7RuZyB0aOG7gyB04bqjaSBkYW5oIHPDoWNoIGNoxrDGoW5nJztcbiAgICAgIHNldEVycm9yKGVycm9yTWVzc2FnZSk7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIGFsbCBjaGFwdGVyczonLCBlcnIpO1xuICAgICAgdGhyb3cgZXJyO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH0sIFtzdG9yeUlkLCBpc0NhY2hlVmFsaWRdKTtcblxuICBjb25zdCBjbGVhckNhY2hlID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIC8vIENsZWFyIGFsbCBjYWNoZSBlbnRyaWVzIGZvciB0aGlzIHN0b3J5XG4gICAgQXJyYXkuZnJvbShjaGFwdGVyc0NhY2hlLmtleXMoKSkuZm9yRWFjaChrZXkgPT4ge1xuICAgICAgaWYgKGtleS5zdGFydHNXaXRoKHN0b3J5SWQpKSB7XG4gICAgICAgIGNoYXB0ZXJzQ2FjaGUuZGVsZXRlKGtleSk7XG4gICAgICB9XG4gICAgfSk7XG4gIH0sIFtzdG9yeUlkXSk7XG5cbiAgcmV0dXJuIHtcbiAgICBjaGFwdGVycyxcbiAgICBsb2FkaW5nLFxuICAgIGVycm9yLFxuICAgIHRvdGFsQ2hhcHRlcnMsXG4gICAgY3VycmVudFBhZ2UsXG4gICAgdG90YWxQYWdlcyxcbiAgICBwYWdlU2l6ZSxcbiAgICBsb2FkQ2hhcHRlcnMsXG4gICAgbG9hZEFsbENoYXB0ZXJzLFxuICAgIGNsZWFyQ2FjaGVcbiAgfTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IHVzZUNoYXB0ZXJzQ2FjaGU7Il0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJmZXRjaENoYXB0ZXJzQnlTdG9yeUlkIiwiY2hhcHRlcnNDYWNoZSIsIk1hcCIsIkNBQ0hFX0RVUkFUSU9OIiwidXNlQ2hhcHRlcnNDYWNoZSIsInN0b3J5SWQiLCJjaGFwdGVycyIsInNldENoYXB0ZXJzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwidG90YWxDaGFwdGVycyIsInNldFRvdGFsQ2hhcHRlcnMiLCJjdXJyZW50UGFnZSIsInNldEN1cnJlbnRQYWdlIiwidG90YWxQYWdlcyIsInNldFRvdGFsUGFnZXMiLCJwYWdlU2l6ZSIsInNldFBhZ2VTaXplIiwiaXNDYWNoZVZhbGlkIiwiZW50cnkiLCJEYXRlIiwibm93IiwibGFzdEZldGNoZWQiLCJsb2FkQ2hhcHRlcnMiLCJwYWdlIiwicGFnZVNpemVQYXJhbSIsImZvcmNlUmVmcmVzaCIsImZpbHRlcnMiLCJmaWx0ZXJLZXkiLCJlbmhhbmNlZF9vbmx5Iiwic2NyYXBlZF9vbmx5IiwiY2FjaGVLZXkiLCJjYWNoZWRFbnRyeSIsImdldCIsIk1hdGgiLCJjZWlsIiwicmVzcG9uc2UiLCJuZXdFbnRyeSIsImRhdGEiLCJwYWdpbmF0aW9uIiwidG90YWxfaXRlbXMiLCJpc0NvbXBsZXRlIiwiaGFzX25leHQiLCJzZXQiLCJlcnIiLCJlcnJvck1lc3NhZ2UiLCJjb25zb2xlIiwibG9hZEFsbENoYXB0ZXJzIiwibWF4UGFnZXMiLCJhbGxDaGFwdGVyc0NhY2hlS2V5IiwiZmlyc3RSZXNwb25zZSIsImFsbENoYXB0ZXJzIiwicGFnZXNUb0xvYWQiLCJtaW4iLCJwcm9taXNlcyIsInB1c2giLCJhZGRpdGlvbmFsUmVzcG9uc2VzIiwiUHJvbWlzZSIsImFsbCIsImFkZGl0aW9uYWxDaGFwdGVycyIsImZsYXRNYXAiLCJyZXMiLCJsZW5ndGgiLCJjbGVhckNhY2hlIiwiQXJyYXkiLCJmcm9tIiwia2V5cyIsImZvckVhY2giLCJrZXkiLCJzdGFydHNXaXRoIiwiZGVsZXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useChaptersCache.ts\n");

/***/ }),

/***/ "(ssr)/./src/services/storyService.ts":
/*!**************************************!*\
  !*** ./src/services/storyService.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchChapterById: () => (/* binding */ fetchChapterById),\n/* harmony export */   fetchChapterByNumber: () => (/* binding */ fetchChapterByNumber),\n/* harmony export */   fetchChaptersByStoryId: () => (/* binding */ fetchChaptersByStoryId),\n/* harmony export */   fetchStories: () => (/* binding */ fetchStories),\n/* harmony export */   fetchStoryById: () => (/* binding */ fetchStoryById)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:8002\" || 0;\nconst API_PREFIX = \"/api/v1/stories\";\n// Fetch all stories with pagination\nconst fetchStories = async (page = 1, pageSize = 20)=>{\n    const response = await fetch(`${API_BASE_URL}${API_PREFIX}?page=${page}&page_size=${pageSize}`);\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch stories\");\n    }\n    return response.json();\n};\n// Fetch a single story by ID\nconst fetchStoryById = async (storyId)=>{\n    const response = await fetch(`${API_BASE_URL}${API_PREFIX}/${storyId}`);\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch story\");\n    }\n    return response.json();\n};\n// Fetch chapters for a story\nconst fetchChaptersByStoryId = async (storyId, page = 1, pageSize = 50, filters)=>{\n    const params = new URLSearchParams({\n        page: page.toString(),\n        page_size: pageSize.toString()\n    });\n    if (filters?.enhanced_only) {\n        params.append(\"enhanced_only\", \"true\");\n    }\n    if (filters?.scraped_only) {\n        params.append(\"scraped_only\", \"true\");\n    }\n    const response = await fetch(`${API_BASE_URL}${API_PREFIX}/${storyId}/chapters?${params.toString()}`);\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch chapters\");\n    }\n    return response.json();\n};\n// Fetch a single chapter by chapter number\nconst fetchChapterByNumber = async (storyId, chapterNumber)=>{\n    const response = await fetch(`${API_BASE_URL}${API_PREFIX}/${storyId}/chapters/${chapterNumber}`, {\n        cache: \"no-store\"\n    });\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch chapter\");\n    }\n    const data = await response.json();\n    // Backend returns ChapterContentResponse which extends APIResponse\n    // Extract the chapter data from the response structure\n    return {\n        id: data.id,\n        story_id: data.story_id,\n        chapter_number: data.chapter_number,\n        title: data.title,\n        url: data.url,\n        original_content: data.original_content,\n        enhanced_content: data.enhanced_content,\n        is_scraped: !!data.original_content,\n        is_enhanced: data.is_enhanced,\n        enhancement_status: data.enhancement_status,\n        word_count: data.word_count,\n        created_at: data.created_at,\n        updated_at: data.updated_at,\n        enhancement_metadata: data.enhancement_metadata\n    };\n};\n// Fetch a single chapter by ID\nconst fetchChapterById = async (chapterId)=>{\n    const response = await fetch(`${API_BASE_URL}/api/v1/chapters/${chapterId}`);\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch chapter\");\n    }\n    return response.json();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/storyService.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e8043e36ccac\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmUvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzk5ODYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlODA0M2UzNmNjYWNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_layout_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Navbar */ \"(rsc)/./src/components/layout/Navbar.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Footer */ \"(rsc)/./src/components/layout/Footer.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"WebTruyen - Your Story Hub\",\n    description: \"A modern web application for scraping and reading stories.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className)} flex flex-col h-full`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-grow\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQU1NQTtBQUppQjtBQUN5QjtBQUNBO0FBSXpDLE1BQU1HLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdSO0lBQ0EscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0MsV0FBVTtrQkFDeEIsNEVBQUNDO1lBQUtELFdBQVcsQ0FBQyxFQUFFViwrSkFBZSxDQUFDLHFCQUFxQixDQUFDOzs4QkFDeEQsOERBQUNDLGlFQUFNQTs7Ozs7OEJBQ1AsOERBQUNXO29CQUFLRixXQUFVOzhCQUFhSDs7Ozs7OzhCQUM3Qiw4REFBQ0wsaUVBQU1BOzs7Ozs7Ozs7Ozs7Ozs7O0FBSWYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mZS8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuaW1wb3J0IE5hdmJhciBmcm9tIFwiQC9jb21wb25lbnRzL2xheW91dC9OYXZiYXJcIjtcbmltcG9ydCBGb290ZXIgZnJvbSBcIkAvY29tcG9uZW50cy9sYXlvdXQvRm9vdGVyXCI7XG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbXCJsYXRpblwiXSB9KTtcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6IFwiV2ViVHJ1eWVuIC0gWW91ciBTdG9yeSBIdWJcIixcbiAgZGVzY3JpcHRpb246IFwiQSBtb2Rlcm4gd2ViIGFwcGxpY2F0aW9uIGZvciBzY3JhcGluZyBhbmQgcmVhZGluZyBzdG9yaWVzLlwiLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIiBjbGFzc05hbWU9XCJoLWZ1bGwgZGFya1wiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtgJHtpbnRlci5jbGFzc05hbWV9IGZsZXggZmxleC1jb2wgaC1mdWxsYH0+XG4gICAgICAgIDxOYXZiYXIgLz5cbiAgICAgICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleC1ncm93XCI+e2NoaWxkcmVufTwvbWFpbj5cbiAgICAgICAgPEZvb3RlciAvPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn0iXSwibmFtZXMiOlsiaW50ZXIiLCJOYXZiYXIiLCJGb290ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImNsYXNzTmFtZSIsImJvZHkiLCJtYWluIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/loading.tsx":
/*!*****************************!*\
  !*** ./src/app/loading.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-500 mb-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-400\",\n                    children: \"Đang tải...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 6,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\loading.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\loading.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xvYWRpbmcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZSxTQUFTQTtJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOzs7Ozs7OEJBQ2YsOERBQUNDO29CQUFFRCxXQUFVOzhCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJckMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mZS8uL3NyYy9hcHAvbG9hZGluZy50c3g/OWNkOSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2FkaW5nKCkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaW5saW5lLWJsb2NrIGFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0xMiB3LTEyIGJvcmRlci1iLTIgYm9yZGVyLWluZGlnby01MDAgbWItNFwiPjwvZGl2PlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+xJBhbmcgdOG6o2kuLi48L3A+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn0iXSwibmFtZXMiOlsiTG9hZGluZyIsImRpdiIsImNsYXNzTmFtZSIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-24 h-24 text-gray-400 mx-auto mb-4\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 1,\n                                d: \"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.562M15 6.306a7.962 7.962 0 00-6 0m6 0V5a2 2 0 00-2-2H9a2 2 0 00-2 2v1.306m8 0V7a2 2 0 012 2v6.414c0 .796-.316 1.559-.879 2.121l-7.07 7.071a2 2 0 01-2.829 0L6.151 17.535A2.99 2.99 0 015.272 15.5L12 8.772l6.728 6.728a2.99 2.99 0 01-.879 2.121z\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 8,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-6xl font-bold text-white mb-4\",\n                            children: \"404\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-gray-300 mb-4\",\n                            children: \"Kh\\xf4ng t\\xecm thấy trang\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 mb-8 max-w-md mx-auto\",\n                            children: \"Trang bạn đang t\\xecm kiếm c\\xf3 thể đ\\xe3 bị x\\xf3a, đổi t\\xean hoặc tạm thời kh\\xf4ng khả dụng.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/\",\n                            className: \"inline-flex items-center px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 mr-2\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this),\n                                \"Về trang chủ\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/scrape\",\n                                className: \"inline-flex items-center px-4 py-2 text-indigo-400 hover:text-indigo-300 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"C\\xe0o truyện mới\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/stories/[storyId]/[chapterId]/page.tsx":
/*!********************************************************!*\
  !*** ./src/app/stories/[storyId]/[chapterId]/page.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReaderPage),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _services_storyService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/storyService */ \"(rsc)/./src/services/storyService.ts\");\n/* harmony import */ var _components_reader_ChapterReaderWrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/reader/ChapterReaderWrapper */ \"(rsc)/./src/components/reader/ChapterReaderWrapper.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n\n\n\n\nasync function ReaderPage({ params }) {\n    try {\n        const chapterNumber = parseInt(params.chapterId);\n        // Only fetch essential data server-side\n        const [story, chapter] = await Promise.all([\n            (0,_services_storyService__WEBPACK_IMPORTED_MODULE_1__.fetchStoryById)(params.storyId),\n            (0,_services_storyService__WEBPACK_IMPORTED_MODULE_1__.fetchChapterByNumber)(params.storyId, chapterNumber)\n        ]);\n        console.log(\"chapter\", chapter);\n        // Navigation will be handled client-side\n        const previousChapter = null;\n        const nextChapter = null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_reader_ChapterReaderWrapper__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            story: story,\n            chapter: chapter,\n            storyId: params.storyId\n        }, void 0, false, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\stories\\\\[storyId]\\\\[chapterId]\\\\page.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this);\n    } catch (error) {\n        console.error(\"Error fetching chapter:\", error);\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.notFound)();\n    }\n}\nasync function generateMetadata({ params }) {\n    try {\n        const chapterNumber = parseInt(params.chapterId);\n        const [story, chapter] = await Promise.all([\n            (0,_services_storyService__WEBPACK_IMPORTED_MODULE_1__.fetchStoryById)(params.storyId),\n            (0,_services_storyService__WEBPACK_IMPORTED_MODULE_1__.fetchChapterByNumber)(params.storyId, chapterNumber)\n        ]);\n        const title = chapter.title ? `${story.title} - Chương ${chapter.chapter_number}: ${chapter.title}` : `${story.title} - Chương ${chapter.chapter_number}`;\n        return {\n            title: `${title} - WebTruyen`,\n            description: `Đọc ${title} của tác giả ${story.author}`\n        };\n    } catch (error) {\n        console.error(\"Error generating metadata:\", error);\n        return {\n            title: \"Chapter Not Found - WebTruyen\"\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/stories/[storyId]/[chapterId]/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Footer = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-zinc-800/30 border-t border-gray-700 mt-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-sm text-gray-400\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        \"\\xa9 \",\n                        new Date().getFullYear(),\n                        \" WebTruyen. All rights reserved.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 6,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 5,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvRm9vdGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUEsTUFBTUEsU0FBUztJQUNiLHFCQUNFLDhEQUFDQztRQUFPQyxXQUFVO2tCQUNoQiw0RUFBQ0M7WUFBSUQsV0FBVTtzQkFDYiw0RUFBQ0M7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNFOzt3QkFBRTt3QkFBUSxJQUFJQyxPQUFPQyxXQUFXO3dCQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSzlDO0FBRUEsaUVBQWVOLE1BQU1BLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mZS8uL3NyYy9jb21wb25lbnRzL2xheW91dC9Gb290ZXIudHN4PzI2MzgiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgRm9vdGVyID0gKCkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxmb290ZXIgY2xhc3NOYW1lPVwiYmctemluYy04MDAvMzAgYm9yZGVyLXQgYm9yZGVyLWdyYXktNzAwIG10LWF1dG9cIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHktNCBweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHRleHQtc20gdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgIDxwPiZjb3B5OyB7bmV3IERhdGUoKS5nZXRGdWxsWWVhcigpfSBXZWJUcnV5ZW4uIEFsbCByaWdodHMgcmVzZXJ2ZWQuPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZm9vdGVyPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgRm9vdGVyOyJdLCJuYW1lcyI6WyJGb290ZXIiLCJmb290ZXIiLCJjbGFzc05hbWUiLCJkaXYiLCJwIiwiRGF0ZSIsImdldEZ1bGxZZWFyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/Navbar.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Navbar.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n\n\nconst Navbar = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-zinc-800/30 border-b border-gray-700 backdrop-blur-2xl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/\",\n                            className: \"text-white font-bold text-xl\",\n                            children: \"WebTruyen\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:block\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-10 flex items-baseline space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"text-gray-300 hover:bg-zinc-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium\",\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/scrape\",\n                                    className: \"text-gray-300 hover:bg-zinc-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium\",\n                                    children: \"Scrape\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                lineNumber: 7,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navbar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/Navbar.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/reader/ChapterReaderWrapper.tsx":
/*!********************************************************!*\
  !*** ./src/components/reader/ChapterReaderWrapper.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Personal Projects\Vibe\Webtruyen\FE\src\components\reader\ChapterReaderWrapper.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/services/storyService.ts":
/*!**************************************!*\
  !*** ./src/services/storyService.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchChapterById: () => (/* binding */ fetchChapterById),\n/* harmony export */   fetchChapterByNumber: () => (/* binding */ fetchChapterByNumber),\n/* harmony export */   fetchChaptersByStoryId: () => (/* binding */ fetchChaptersByStoryId),\n/* harmony export */   fetchStories: () => (/* binding */ fetchStories),\n/* harmony export */   fetchStoryById: () => (/* binding */ fetchStoryById)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:8002\" || 0;\nconst API_PREFIX = \"/api/v1/stories\";\n// Fetch all stories with pagination\nconst fetchStories = async (page = 1, pageSize = 20)=>{\n    const response = await fetch(`${API_BASE_URL}${API_PREFIX}?page=${page}&page_size=${pageSize}`);\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch stories\");\n    }\n    return response.json();\n};\n// Fetch a single story by ID\nconst fetchStoryById = async (storyId)=>{\n    const response = await fetch(`${API_BASE_URL}${API_PREFIX}/${storyId}`);\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch story\");\n    }\n    return response.json();\n};\n// Fetch chapters for a story\nconst fetchChaptersByStoryId = async (storyId, page = 1, pageSize = 50, filters)=>{\n    const params = new URLSearchParams({\n        page: page.toString(),\n        page_size: pageSize.toString()\n    });\n    if (filters?.enhanced_only) {\n        params.append(\"enhanced_only\", \"true\");\n    }\n    if (filters?.scraped_only) {\n        params.append(\"scraped_only\", \"true\");\n    }\n    const response = await fetch(`${API_BASE_URL}${API_PREFIX}/${storyId}/chapters?${params.toString()}`);\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch chapters\");\n    }\n    return response.json();\n};\n// Fetch a single chapter by chapter number\nconst fetchChapterByNumber = async (storyId, chapterNumber)=>{\n    const response = await fetch(`${API_BASE_URL}${API_PREFIX}/${storyId}/chapters/${chapterNumber}`, {\n        cache: \"no-store\"\n    });\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch chapter\");\n    }\n    const data = await response.json();\n    // Backend returns ChapterContentResponse which extends APIResponse\n    // Extract the chapter data from the response structure\n    return {\n        id: data.id,\n        story_id: data.story_id,\n        chapter_number: data.chapter_number,\n        title: data.title,\n        url: data.url,\n        original_content: data.original_content,\n        enhanced_content: data.enhanced_content,\n        is_scraped: !!data.original_content,\n        is_enhanced: data.is_enhanced,\n        enhancement_status: data.enhancement_status,\n        word_count: data.word_count,\n        created_at: data.created_at,\n        updated_at: data.updated_at,\n        enhancement_metadata: data.enhancement_metadata\n    };\n};\n// Fetch a single chapter by ID\nconst fetchChapterById = async (chapterId)=>{\n    const response = await fetch(`${API_BASE_URL}/api/v1/chapters/${chapterId}`);\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch chapter\");\n    }\n    return response.json();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/storyService.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fstories%2F%5BstoryId%5D%2F%5BchapterId%5D%2Fpage&page=%2Fstories%2F%5BstoryId%5D%2F%5BchapterId%5D%2Fpage&appPaths=%2Fstories%2F%5BstoryId%5D%2F%5BchapterId%5D%2Fpage&pagePath=private-next-app-dir%2Fstories%2F%5BstoryId%5D%2F%5BchapterId%5D%2Fpage.tsx&appDir=D%3A%5CPersonal%20Projects%5CVibe%5CWebtruyen%5CFE%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CPersonal%20Projects%5CVibe%5CWebtruyen%5CFE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();