import React from 'react';

/**
 * Utility functions for chapter processing and display
 */

/**
 * Cleans up chapter titles by removing redundant prefixes and formatting
 * @param title - The original chapter title
 * @param chapterNumber - The chapter number
 * @returns Cleaned chapter title
 */
export const cleanChapterTitle = (title: string | undefined, chapterNumber: number): string => {
  if (!title || title.trim() === '') {
    return '';
  }

  let cleanedTitle = title.trim();

  // Remove common redundant prefixes
  const prefixPatterns = [
    /^Chương\s*\d+[:\s,]*\s*/i,
    /^Chapter\s*\d+[:\s,]*\s*/i,
    /^Ch\s*\d+[:\s,]*\s*/i,
    /^\d+[:\s,]*\s*/,
  ];

  // Apply each pattern to remove redundant prefixes
  for (const pattern of prefixPatterns) {
    cleanedTitle = cleanedTitle.replace(pattern, '');
  }

  // Remove multiple consecutive spaces and trim
  cleanedTitle = cleanedTitle.replace(/\s+/g, ' ').trim();

  // If the cleaned title is empty or just punctuation, return empty string
  if (!cleanedTitle || /^[:\s,.-]*$/.test(cleanedTitle)) {
    return '';
  }

  return cleanedTitle;
};

/**
 * Formats chapter display name with number and cleaned title
 * @param chapterNumber - The chapter number
 * @param title - The original chapter title
 * @returns Formatted chapter display name
 */
export const formatChapterDisplayName = (chapterNumber: number, title?: string): string => {
  const cleanedTitle = cleanChapterTitle(title, chapterNumber);
  
  if (cleanedTitle) {
    return `Chương ${chapterNumber}: ${cleanedTitle}`;
  }
  
  return `Chương ${chapterNumber}`;
};

/**
 * Gets the status badge configuration for a chapter
 * @param chapter - The chapter object
 * @returns Status badge configuration
 */
export const getChapterStatusBadge = (chapter: { is_enhanced: boolean; is_scraped: boolean }) => {
  let text: string;
  let className: string;
  let icon: JSX.Element;

  if (chapter.is_enhanced) {
    text = 'Đã nâng cấp';
    className = 'inline-flex items-center gap-1 text-xs text-blue-100 bg-blue-500/80 px-2 py-1 rounded-full font-medium';
    icon = (
      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
      </svg>
    );
  } else if (chapter.is_scraped) {
    text = 'Đã cào';
    className = 'inline-flex items-center gap-1 text-xs text-green-100 bg-green-500/80 px-2 py-1 rounded-full font-medium';
    icon = (
      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
      </svg>
    );
  } else {
    text = 'Chưa cào';
    className = 'inline-flex items-center gap-1 text-xs text-gray-300 bg-gray-500/60 px-2 py-1 rounded-full font-medium';
    icon = (
      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
      </svg>
    );
  }

  return (
    <span className={className}>
      {icon}
      {text}
    </span>
  );
};