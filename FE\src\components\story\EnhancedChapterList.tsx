'use client';

/**
 * Enhanced Chapter List Component
 * 
 * Features:
 * - Improved UI/UX with modern design
 * - Enhanced batch scraping with real-time progress
 * - Toast notifications for user feedback
 * - Background scraping with immediate chapter access
 * - Detailed error handling and retry mechanisms
 * - Vietnamese language support
 */

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import Link from 'next/link';
import { Chapter } from '../../types/story';
import { useChaptersCache } from '../../hooks/useChaptersCache';
import { scrapeChapterContent } from '../../services/hierarchicalScrapeService';
import { 
  enhancedBatchScrapingService, 
  BatchProgressResponse,
  ChapterScrapeResult
} from '../../services/enhancedBatchScrapingService';
import ChapterFilterModal from './ChapterFilterModal';
import ChapterPagination from './ChapterPagination';
import ChapterStatusFilter from './ChapterStatusFilter';
import type { ChapterFilter } from './ChapterStatusFilter';
import { cleanChapterTitle, getChapterStatusBadge } from '../../utils/chapterUtils';
import { ToastContainer, useToast } from '../ui/Toast';

// ============================================================================
// Types and Interfaces
// ============================================================================

interface EnhancedChapterListProps {
  storyId: string;
  chapters?: Chapter[];
  onChapterUpdate?: (chapter: Chapter) => void;
}

interface ScrapingProgress {
  [chapterId: string]: {
    status: 'pending' | 'scraping' | 'completed' | 'failed';
    error?: string;
    retryCount?: number;
  };
}

interface BatchScrapingState {
  isActive: boolean;
  jobId?: string;
  progress: BatchProgressResponse | null;
  startTime?: Date;
}

// ============================================================================
// Enhanced Chapter List Component
// ============================================================================

export const EnhancedChapterList: React.FC<EnhancedChapterListProps> = ({
  storyId,
  chapters: propChapters,
  onChapterUpdate
}) => {
  // ========================================================================
  // Hooks and State Management
  // ========================================================================
  
  // Use chapters cache when chapters are not provided as props
  const {
    chapters: cachedChapters,
    loading: chaptersLoading,
    totalChapters,
    currentPage: cachePage,
    totalPages: cachedTotalPages,
    loadChapters
  } = useChaptersCache(storyId);
  
  // Use prop chapters if provided, otherwise use cached chapters
  const chapters = propChapters || cachedChapters;
  
  const [selectedChapters, setSelectedChapters] = useState<Set<string>>(new Set());
  const [scrapingProgress, setScrapingProgress] = useState<ScrapingProgress>({});
  const [batchScrapingState, setBatchScrapingState] = useState<BatchScrapingState>({
    isActive: false,
    progress: null
  });
  const [popup, setPopup] = useState<{ chapterId: string; chapterNumber: number } | null>(null);
  const [showBatchControls, setShowBatchControls] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [filter, setFilter] = useState<any>({});
  const [itemsPerPage, setItemsPerPage] = useState(50);
  const [currentPage, setCurrentPage] = useState(1);
  
  // Toast notifications
  const {
    toasts,
    removeToast,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showProgress,
    updateToast
  } = useToast();
  
  // Load chapters if not provided as props
  useEffect(() => {
    if (!propChapters && !chaptersLoading) {
      loadChapters(currentPage, itemsPerPage);
    }
  }, [propChapters, chaptersLoading, currentPage, itemsPerPage, loadChapters]);

  // ========================================================================
  // Computed Values
  // ========================================================================
  
  const filteredChapters = useMemo(() => {
    if (!chapters || chapters.length === 0) return [];
    
    let filtered = chapters;
    
    // Apply filters
    if (filter.scraped !== null && filter.scraped !== undefined) {
      filtered = filtered.filter(chapter => {
        return chapter.is_scraped === filter.scraped;
      });
    }

    if (filter.enhanced !== null && filter.enhanced !== undefined) {
      filtered = filtered.filter(chapter => {
        return chapter.is_enhanced === filter.enhanced;
      });
    }

    if (filter.search) {
      const searchTerm = filter.search.toLowerCase();
      filtered = filtered.filter(chapter =>
        chapter.title.toLowerCase().includes(searchTerm) ||
        chapter.chapter_number.toString().includes(searchTerm)
      );
    }

    return filtered.sort((a, b) => a.chapter_number - b.chapter_number);
  }, [chapters, filter]);
  
  const paginatedChapters = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredChapters.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredChapters, currentPage, itemsPerPage]);
  
  // Use total pages from API response instead of calculating from filtered chapters
  const calculatedTotalPages = propChapters ? Math.ceil(filteredChapters.length / itemsPerPage) : cachedTotalPages;
  
  const scrapingStats = useMemo(() => {
    const total = filteredChapters.length;
    const scraped = filteredChapters.filter(ch => ch.is_scraped).length;
    const unscraped = total - scraped;
    const selected = selectedChapters.size;
    
    return { total, scraped, unscraped, selected };
  }, [filteredChapters, selectedChapters]);

  // ========================================================================
  // Effects
  // ========================================================================
  
  useEffect(() => {
    // Clear selected chapters when filter changes
    setSelectedChapters(new Set());
    setCurrentPage(1);
  }, [filter]);
  
  useEffect(() => {
    // Cleanup batch scraping service on unmount
    return () => {
      enhancedBatchScrapingService.cleanup();
    };
  }, []);

  // ========================================================================
  // Individual Chapter Scraping
  // ========================================================================
  
  const handleScrapeChapter = useCallback(async (chapter: Chapter) => {
    const chapterId = chapter.id;
    
    // Update progress
    setScrapingProgress(prev => ({
      ...prev,
      [chapterId]: { status: 'scraping' }
    }));
    
    try {
      const scrapedChapter = await scrapeChapterContent(chapterId);
      
      // Update progress
      setScrapingProgress(prev => ({
        ...prev,
        [chapterId]: { status: 'completed' }
      }));
      
      // Update chapter with scraped content
      const updatedChapter: Chapter = {
        ...chapter,
        original_content: scrapedChapter.content,
        is_scraped: true,
        updated_at: new Date().toISOString()
      };
      
      // Notify parent
      if (onChapterUpdate) {
        onChapterUpdate(updatedChapter);
      }
      
      // Show success toast
      showSuccess(
        'Cào chương thành công',
        `Chương ${chapter.chapter_number}: ${cleanChapterTitle(chapter.title, chapter.chapter_number)}`
      );
      
      // Close popup if open
      setPopup(null);
      
    } catch (error: any) {
      console.error('Error scraping chapter:', error);
      
      const currentProgress = scrapingProgress[chapterId];
      const retryCount = (currentProgress?.retryCount || 0) + 1;
      const maxRetries = 3;
      
      if (retryCount < maxRetries) {
        // Auto-retry
        setScrapingProgress(prev => ({
          ...prev,
          [chapterId]: { 
            status: 'pending', 
            error: error.message,
            retryCount 
          }
        }));
        
        showWarning(
          'Đang thử lại',
          `Chương ${chapter.chapter_number} - Lần thử ${retryCount}/${maxRetries}`
        );
        
        // Retry after delay
        setTimeout(() => {
          handleScrapeChapter(chapter);
        }, 2000 * retryCount);
        
      } else {
        // Max retries reached
        setScrapingProgress(prev => ({
          ...prev,
          [chapterId]: { 
            status: 'failed', 
            error: error.message,
            retryCount 
          }
        }));
        
        showError(
          'Cào chương thất bại',
          `Chương ${chapter.chapter_number}: ${error.message}`,
          8000
        );
      }
    }
  }, [scrapingProgress, onChapterUpdate, showSuccess, showWarning, showError]);

  // ========================================================================
  // Chapter Selection
  // ========================================================================
  
  const handleSelectChapter = useCallback((chapterId: string) => {
    setSelectedChapters(prev => {
      const newSet = new Set(prev);
      if (newSet.has(chapterId)) {
        newSet.delete(chapterId);
      } else {
        newSet.add(chapterId);
      }
      return newSet;
    });
  }, []);
  
  const handleSelectAll = useCallback(() => {
    const unscrapedChapters = paginatedChapters
      .filter(ch => !ch.is_scraped)
      .map(ch => ch.id);
    setSelectedChapters(new Set(unscrapedChapters));
  }, [paginatedChapters]);
  
  const handleDeselectAll = useCallback(() => {
    setSelectedChapters(new Set());
  }, []);

  // ========================================================================
  // Enhanced Batch Scraping
  // ========================================================================
  
  const handleBatchScrape = useCallback(async () => {
    if (selectedChapters.size === 0) {
      showWarning('Chưa chọn chương', 'Vui lòng chọn ít nhất một chương để cào.');
      return;
    }
    
    const chapterIds = Array.from(selectedChapters);
    const startTime = new Date();
    
    // Initialize batch scraping state
    setBatchScrapingState({
      isActive: true,
      progress: null,
      startTime
    });
    
    // Show initial progress toast
    const progressToastId = showProgress(
      'Bắt đầu cào hàng loạt',
      `Đang cào ${chapterIds.length} chương...`,
      0
    );
    
    try {
      // Start batch scraping with progress tracking
      const jobId = await enhancedBatchScrapingService.batchScrapeWithProgress(
        chapterIds,
        {
          maxConcurrent: 10,
          rateLimitDelay: 2.0,
          onProgress: (progress: BatchProgressResponse) => {
            // Update batch scraping state
            setBatchScrapingState(prev => ({
              ...prev,
              progress,
              jobId: progress.job_id
            }));
            
            // Update progress toast
            updateToast(progressToastId, {
              title: 'Đang cào hàng loạt',
              message: `${progress.completed_chapters}/${progress.total_chapters} chương hoàn thành`,
              progress: progress.progress_percentage
            });
            
            // Update individual chapter progress
            const newProgress: ScrapingProgress = {};
            progress.results.forEach((result: ChapterScrapeResult) => {
              newProgress[result.chapter_id] = {
                status: result.status === 'success' ? 'completed' : 
                       result.status === 'failed' ? 'failed' : 'pending',
                error: result.error_message
              };
            });
            setScrapingProgress(prev => ({ ...prev, ...newProgress }));
            
            // Update cached chapters for completed ones
            progress.results
              .filter(result => result.status === 'success')
              .forEach(result => {
                const chapter = cachedChapters.find(ch => ch.id === result.chapter_id);
                if (chapter) {
                  // Trigger a refresh of the chapter data
                  // This would typically involve fetching the updated chapter from the API
                  // For now, we'll mark it as needing refresh
                  // TODO: Implement proper chapter refresh
                }
              });
          },
          onComplete: (results) => {
            // Batch scraping completed
            setBatchScrapingState({
              isActive: false,
              progress: null
            });
            
            // Remove progress toast and show completion toast
            removeToast(progressToastId);
            
            if (results.successful_count > 0) {
              showSuccess(
                'Cào hàng loạt hoàn thành',
                `Đã cào thành công ${results.successful_count}/${results.total_requested} chương`,
                10000
              );
            }
            
            if (results.failed_count > 0) {
              showError(
                'Một số chương cào thất bại',
                `${results.failed_count} chương không thể cào được. Xem chi tiết bên dưới.`,
                10000
              );
            }
            
            // Clear selected chapters
            setSelectedChapters(new Set());
          },
          onError: (error) => {
            setBatchScrapingState({
              isActive: false,
              progress: null
            });
            
            removeToast(progressToastId);
            showError(
              'Lỗi cào hàng loạt',
              error.message,
              10000
            );
          }
        }
      );
      
      if (jobId) {
        setBatchScrapingState(prev => ({ ...prev, jobId }));
      }
      
    } catch (error: any) {
      setBatchScrapingState({
        isActive: false,
        progress: null
      });
      
      removeToast(progressToastId);
      
      // Provide specific error messages based on error type
      let errorTitle = 'Không thể bắt đầu cào hàng loạt';
      let errorMessage = error.message;
      
      if (error.message?.includes('Database')) {
        errorTitle = 'Lỗi cơ sở dữ liệu';
        errorMessage = 'Cơ sở dữ liệu tạm thời không khả dụng. Vui lòng thử lại sau vài giây.';
      } else if (error.message?.includes('Network') || error.message?.includes('fetch')) {
        errorTitle = 'Lỗi kết nối';
        errorMessage = 'Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối internet.';
      } else if (error.message?.includes('503')) {
        errorTitle = 'Dịch vụ tạm thời không khả dụng';
        errorMessage = 'Máy chủ đang bận. Vui lòng thử lại sau vài phút.';
      }
      
      showError(errorTitle, errorMessage, 10000);
      
      // Clear selected chapters on error
      setSelectedChapters(new Set());
    }
  }, [selectedChapters, cachedChapters, showProgress, showSuccess, showError, showWarning, updateToast, removeToast]);
  
  const handleCancelBatchScraping = useCallback(async () => {
    if (batchScrapingState.jobId) {
      try {
        await enhancedBatchScrapingService.cancelBatchJob(batchScrapingState.jobId);
        showInfo('Đã hủy cào hàng loạt', 'Quá trình cào hàng loạt đã được hủy.');
      } catch (error: any) {
        showError('Không thể hủy', error.message);
      }
    }
    
    setBatchScrapingState({
      isActive: false,
      progress: null
    });
  }, [batchScrapingState.jobId, showInfo, showError]);

  // ========================================================================
  // Progress Indicators
  // ========================================================================
  
  const getProgressIndicator = (chapterId: string) => {
    const progress = scrapingProgress[chapterId];
    if (!progress) return null;
    
    const iconClass = 'w-4 h-4';
    
    switch (progress.status) {
      case 'pending':
        return (
          <div className="flex items-center justify-center w-6 h-6 bg-yellow-100 rounded-full">
            <div className="w-3 h-3 bg-yellow-400 rounded-full animate-pulse" />
          </div>
        );
      case 'scraping':
        return (
          <div className="flex items-center justify-center w-6 h-6 bg-blue-100 rounded-full">
            <svg className={`${iconClass} text-blue-600 animate-spin`} fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
              <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
            </svg>
          </div>
        );
      case 'completed':
        return (
          <div className="flex items-center justify-center w-6 h-6 bg-green-100 rounded-full">
            <svg className={`${iconClass} text-green-600`} fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </div>
        );
      case 'failed':
        return (
          <div className="flex items-center justify-center w-6 h-6 bg-red-100 rounded-full">
            <svg className={`${iconClass} text-red-600`} fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </div>
        );
      default:
        return null;
    }
  };

  // ========================================================================
  // Render Methods
  // ========================================================================
  
  const renderBatchControls = () => {
    if (!showBatchControls && selectedChapters.size === 0) return null;
    
    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-4">
            <h3 className="text-lg font-semibold text-blue-900">Cào hàng loạt</h3>
            <div className="text-sm text-blue-700">
              Đã chọn: <span className="font-medium">{selectedChapters.size}</span> chương
            </div>
          </div>
          
          <button
            onClick={() => setShowBatchControls(!showBatchControls)}
            className="text-blue-600 hover:text-blue-800 transition-colors"
          >
            {showBatchControls ? 'Ẩn' : 'Hiện'} điều khiển
          </button>
        </div>
        
        {showBatchControls && (
          <div className="space-y-4">
            {/* Selection Controls */}
            <div className="flex gap-2">
              <button
                onClick={handleSelectAll}
                className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
              >
                Chọn tất cả (chưa cào)
              </button>
              <button
                onClick={handleDeselectAll}
                className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
              >
                Bỏ chọn tất cả
              </button>
            </div>
            
            {/* Batch Progress */}
            {batchScrapingState.isActive && batchScrapingState.progress && (
              <div className="bg-white rounded-lg p-3 border">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Tiến độ cào hàng loạt</span>
                  <span className="text-sm text-gray-600">
                    {batchScrapingState.progress.completed_chapters}/{batchScrapingState.progress.total_chapters}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${batchScrapingState.progress.progress_percentage}%` }}
                  />
                </div>
                <div className="text-xs text-gray-500">
                  {batchScrapingState.progress.current_chapter && (
                    <span>Đang cào: {batchScrapingState.progress.current_chapter}</span>
                  )}
                </div>
              </div>
            )}
            
            {/* Action Buttons */}
            <div className="flex gap-2">
              {!batchScrapingState.isActive ? (
                <button
                  onClick={handleBatchScrape}
                  disabled={selectedChapters.size === 0}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                >
                  Bắt đầu cào ({selectedChapters.size} chương)
                </button>
              ) : (
                <button
                  onClick={handleCancelBatchScraping}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Hủy cào hàng loạt
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };
  
  const renderStatsBar = () => (
    <div className="bg-slate-800 border border-slate-700 rounded-lg p-4 mb-6">
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
        <div>
          <div className="text-2xl font-bold text-white">{scrapingStats.total}</div>
          <div className="text-sm text-slate-400">Tổng chương</div>
        </div>
        <div>
          <div className="text-2xl font-bold text-green-400">{scrapingStats.scraped}</div>
          <div className="text-sm text-slate-400">Đã cào</div>
        </div>
        <div>
          <div className="text-2xl font-bold text-orange-400">{scrapingStats.unscraped}</div>
          <div className="text-sm text-slate-400">Chưa cào</div>
        </div>
        <div>
          <div className="text-2xl font-bold text-blue-400">{scrapingStats.selected}</div>
          <div className="text-sm text-slate-400">Đã chọn</div>
        </div>
      </div>
    </div>
  );

  // ========================================================================
  // Main Render
  // ========================================================================
  
  if (chaptersLoading) {
    return (
      <div className="flex items-center justify-center py-12 bg-slate-900 min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400" />
        <span className="ml-3 text-slate-300">Đang tải danh sách chương...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6 bg-slate-900 min-h-screen p-6">
      {/* Toast Container */}
      <ToastContainer toasts={toasts} onDismiss={removeToast} position="top-right" />
      
      {/* Stats Bar */}
      {renderStatsBar()}
      
      {/* Filter Controls */}
      <ChapterFilterModal 
        filter={filter} 
        onFilterChange={setFilter} 
        stats={scrapingStats} 
      />
      {
        filteredChapters.length
      }
      "/"
      {calculatedTotalPages}
      {/* Pagination Controls - Moved to top */}
      {(propChapters ? filteredChapters.length > 0 : totalChapters > 0) && calculatedTotalPages > 1 && (
        <ChapterPagination
          currentPage={currentPage}
          totalPages={calculatedTotalPages}
          totalItems={propChapters ? filteredChapters.length : totalChapters}
          onPageChange={setCurrentPage}
          itemsPerPage={itemsPerPage}
          onItemsPerPageChange={setItemsPerPage}
        />
      )}
      
      {/* Batch Controls */}
      {renderBatchControls()}
      
      {/* Error Message */}
      {errorMessage && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-red-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <p className="text-red-800">{errorMessage}</p>
          </div>
        </div>
      )}
      
      {/* Chapter List */}
      {paginatedChapters.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-slate-400 text-lg mb-2">Không tìm thấy chương nào</div>
          <div className="text-slate-500 text-sm">Thử thay đổi bộ lọc hoặc tìm kiếm</div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {paginatedChapters.map((chapter) => {
            // Use is_scraped boolean instead of checking content directly
            const isSelected = selectedChapters.has(chapter.id);
            const progressIndicator = getProgressIndicator(chapter.id);
            
            return (
              <div
                key={chapter.id}
                className={`group relative bg-slate-800 border border-slate-700 rounded-lg p-3 transition-all duration-200 hover:bg-slate-750 hover:border-slate-600 ${
                  isSelected ? 'border-blue-500 bg-slate-750' : ''
                }`}
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {/* Selection Checkbox - only show for unscraped chapters */}
                    {!chapter.is_scraped && (
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => handleSelectChapter(chapter.id)}
                        className="w-4 h-4 text-blue-600 bg-slate-700 border-slate-600 rounded focus:ring-blue-500 focus:ring-2"
                      />
                    )}
                    <span className="text-xs font-medium text-slate-400">
                      Chương {chapter.chapter_number}:
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    {/* Show progress indicator if scraping, otherwise show status badge */}
                    {progressIndicator || getChapterStatusBadge(chapter)}
                  </div>
                </div>
                
                {/* Chapter Title */}
                <div className="mb-2">
                  {chapter.is_scraped ? (
                    <Link
                      href={`/stories/${storyId}/${chapter.chapter_number}`}
                      className="block text-white hover:text-blue-400 font-medium text-sm transition-colors line-clamp-2"
                    >
                      {cleanChapterTitle(chapter.title, chapter.chapter_number)}
                    </Link>
                  ) : (
                    <div
                      onClick={() => setPopup({ chapterId: chapter.id, chapterNumber: chapter.chapter_number })}
                      className="cursor-pointer text-slate-300 hover:text-white font-medium text-sm transition-colors line-clamp-2"
                    >
                      {cleanChapterTitle(chapter.title, chapter.chapter_number)}
                    </div>
                  )}
                </div>
                
                {/* Chapter Metadata */}
                <div className="flex items-center justify-between text-xs text-slate-500">
                  <div className="flex items-center gap-2">
                    {chapter.updated_at && (
                      <span>{new Date(chapter.updated_at).toLocaleDateString('vi-VN')}</span>
                    )}
                  </div>
                  
                  {/* Action Buttons */}
                  {!chapter.is_scraped && (
                    <button
                      onClick={() => handleScrapeChapter(chapter)}
                      disabled={scrapingProgress[chapter.id]?.status === 'scraping'}
                      className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {scrapingProgress[chapter.id]?.status === 'scraping' ? 'Đang cào...' : 'Cào ngay'}
                    </button>
                  )}
                </div>
                
                {/* Error Display */}
                {scrapingProgress[chapter.id]?.status === 'failed' && (
                  <div className="mt-2 p-2 bg-red-900/50 border border-red-700 rounded text-xs">
                    <div className="flex items-center justify-between">
                      <div className="text-red-300">
                        <strong>Lỗi:</strong> {scrapingProgress[chapter.id]?.error}
                      </div>
                      <button
                        onClick={() => handleScrapeChapter(chapter)}
                        className="px-2 py-1 bg-red-700 text-red-100 rounded hover:bg-red-600 transition-colors"
                      >
                        Thử lại
                      </button>
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}
      
      {/* Pagination - Bottom (duplicate for convenience) */}
      {paginatedChapters.length > 0 && calculatedTotalPages > 1 && (
        <ChapterPagination
          currentPage={currentPage}
          totalPages={calculatedTotalPages}
          totalItems={filteredChapters.length}
          onPageChange={setCurrentPage}
          itemsPerPage={itemsPerPage}
          onItemsPerPageChange={setItemsPerPage}
        />
      )}
      
      {/* Popup for Unscraped Chapters */}
      {popup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">
              Chương {popup.chapterNumber} chưa được cào
            </h3>
            <p className="text-gray-600 mb-6">
              Chương này chưa có nội dung. Bạn có muốn cào ngay bây giờ không?
            </p>
            <div className="flex gap-3 justify-end">
              <button
                onClick={() => setPopup(null)}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Hủy
              </button>
              <button
                onClick={() => {
                  const chapter = chapters.find(ch => ch.id === popup.chapterId);
                  if (chapter) {
                    handleScrapeChapter(chapter);
                  }
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Cào ngay
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedChapterList;