/**
 * Enhanced Batch Scraping Service
 * 
 * Provides advanced batch scraping functionality with:
 * - Specific chapter ID targeting
 * - Real-time progress tracking
 * - Detailed error reporting
 * - Background processing support
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';

// Helper function to make API requests
const makeApiRequest = async <T>(endpoint: string, options: RequestInit = {}): Promise<T> => {
  const url = `${API_BASE_URL}${endpoint}`;
  const response = await fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
  });

  if (!response.ok) {
    throw new Error(`API request failed: ${response.status} ${response.statusText}`);
  }

  return response.json();
};

// ============================================================================
// Types and Interfaces
// ============================================================================

export interface BatchScrapeRequest {
  chapter_ids: string[];
  max_concurrent?: number;
  rate_limit_delay?: number;
  background?: boolean;
}

export interface ChapterScrapeResult {
  chapter_id: string;
  chapter_number: number;
  status: 'success' | 'failed' | 'skipped';
  error_message?: string;
  scraped_at?: string;
  word_count?: number;
}

export interface BatchScrapeResponse {
  job_id?: string;
  success: boolean;
  message: string;
  total_requested: number;
  successful_count: number;
  failed_count: number;
  skipped_count: number;
  results: ChapterScrapeResult[];
  started_at: string;
  completed_at?: string;
  estimated_completion?: string;
}

export interface BatchProgressResponse {
  job_id: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress_percentage: number;
  total_chapters: number;
  completed_chapters: number;
  failed_chapters: number;
  current_chapter?: string;
  estimated_completion?: string;
  results: ChapterScrapeResult[];
}

export interface BatchJobInfo {
  job_id: string;
  status: string;
  progress_percentage: number;
  total_chapters: number;
  completed_chapters: number;
  failed_chapters: number;
}

// ============================================================================
// Enhanced Batch Scraping Service
// ============================================================================

class EnhancedBatchScrapingService {
  private activeJobs = new Map<string, BatchProgressResponse>();
  private progressCallbacks = new Map<string, (progress: BatchProgressResponse) => void>();
  private pollingIntervals = new Map<string, NodeJS.Timeout>();

  /**
   * Start batch scraping for specific chapters
   */
  async startBatchScraping(
    chapterIds: string[],
    options: {
      maxConcurrent?: number;
      rateLimitDelay?: number;
      background?: boolean;
      onProgress?: (progress: BatchProgressResponse) => void;
    } = {}
  ): Promise<BatchScrapeResponse> {
    const request: BatchScrapeRequest = {
      chapter_ids: chapterIds,
      max_concurrent: options.maxConcurrent || 3,
      rate_limit_delay: options.rateLimitDelay || 2.0,
      background: options.background || false
    };

    try {
      const response = await makeApiRequest<BatchScrapeResponse>(
        '/batch-scraping/chapters',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(request)
        }
      );

      // If background processing and we have a job ID, start polling
      if (response.job_id && options.background && options.onProgress) {
        this.startProgressPolling(response.job_id, options.onProgress);
      }

      return response;
    } catch (error) {
      console.error('Error starting batch scraping:', error);
      throw error;
    }
  }

  /**
   * Get progress for a specific batch job
   */
  async getBatchProgress(jobId: string): Promise<BatchProgressResponse> {
    try {
      const progress = await makeApiRequest<BatchProgressResponse>(
        `/batch-scraping/progress/${jobId}`
      );
      
      // Update local cache
      this.activeJobs.set(jobId, progress);
      
      return progress;
    } catch (error) {
      console.error(`Error getting progress for job ${jobId}:`, error);
      throw error;
    }
  }

  /**
   * Start polling for progress updates
   */
  startProgressPolling(
    jobId: string,
    onProgress: (progress: BatchProgressResponse) => void,
    intervalMs: number = 2000
  ): void {
    // Store callback
    this.progressCallbacks.set(jobId, onProgress);

    // Clear existing interval if any
    const existingInterval = this.pollingIntervals.get(jobId);
    if (existingInterval) {
      clearInterval(existingInterval);
    }

    // Start polling
    const interval = setInterval(async () => {
      try {
        const progress = await this.getBatchProgress(jobId);
        
        // Call progress callback
        onProgress(progress);
        
        // Stop polling if job is completed, failed, or cancelled
        if (['completed', 'failed', 'cancelled'].includes(progress.status)) {
          this.stopProgressPolling(jobId);
        }
      } catch (error) {
        console.error(`Error polling progress for job ${jobId}:`, error);
        // Continue polling on error, but log it
      }
    }, intervalMs);

    this.pollingIntervals.set(jobId, interval);
  }

  /**
   * Stop polling for a specific job
   */
  stopProgressPolling(jobId: string): void {
    const interval = this.pollingIntervals.get(jobId);
    if (interval) {
      clearInterval(interval);
      this.pollingIntervals.delete(jobId);
    }
    
    this.progressCallbacks.delete(jobId);
  }

  /**
   * Cancel a running batch job
   */
  async cancelBatchJob(jobId: string): Promise<{ message: string }> {
    try {
      const response = await makeApiRequest<{ message: string }>(
        `/batch-scraping/jobs/${jobId}`,
        { method: 'DELETE' }
      );
      
      // Stop polling
      this.stopProgressPolling(jobId);
      
      return response;
    } catch (error) {
      console.error(`Error cancelling job ${jobId}:`, error);
      throw error;
    }
  }

  /**
   * List all active batch jobs
   */
  async listActiveJobs(): Promise<BatchProgressResponse[]> {
    try {
      return await makeApiRequest<BatchProgressResponse[]>('/batch-scraping/jobs');
    } catch (error) {
      console.error('Error listing active jobs:', error);
      throw error;
    }
  }

  /**
   * Get cached progress for a job (without API call)
   */
  getCachedProgress(jobId: string): BatchProgressResponse | undefined {
    return this.activeJobs.get(jobId);
  }

  /**
   * Clean up all polling intervals (call on component unmount)
   */
  cleanup(): void {
    this.pollingIntervals.forEach((interval) => clearInterval(interval));
    this.pollingIntervals.clear();
    this.progressCallbacks.clear();
    this.activeJobs.clear();
  }

  /**
   * Batch scrape with real-time progress tracking
   * This is a convenience method that combines starting and tracking
   */
  async batchScrapeWithProgress(
    chapterIds: string[],
    options: {
      maxConcurrent?: number;
      rateLimitDelay?: number;
      onProgress?: (progress: BatchProgressResponse) => void;
      onComplete?: (results: BatchScrapeResponse) => void;
      onError?: (error: Error) => void;
    } = {}
  ): Promise<string | null> {
    try {
      // Start batch scraping in background
      const response = await this.startBatchScraping(chapterIds, {
        ...options,
        background: true
      });

      if (!response.job_id) {
        // Immediate completion (no background job)
        if (options.onComplete) {
          options.onComplete(response);
        }
        return null;
      }

      // Set up progress tracking with completion handling
      const originalOnProgress = options.onProgress;
      const enhancedOnProgress = (progress: BatchProgressResponse) => {
        if (originalOnProgress) {
          originalOnProgress(progress);
        }
        
        // Check for completion
        if (progress.status === 'completed' && options.onComplete) {
          const completedResponse: BatchScrapeResponse = {
            job_id: progress.job_id,
            success: true,
            message: 'Batch scraping completed',
            total_requested: progress.total_chapters,
            successful_count: progress.completed_chapters,
            failed_count: progress.failed_chapters,
            skipped_count: 0,
            results: progress.results,
            started_at: response.started_at,
            completed_at: new Date().toISOString()
          };
          options.onComplete(completedResponse);
        }
      };

      // Start progress polling
      this.startProgressPolling(response.job_id, enhancedOnProgress);

      return response.job_id;
    } catch (error) {
      if (options.onError) {
        options.onError(error as Error);
      }
      throw error;
    }
  }
}

// ============================================================================
// Service Instance
// ============================================================================

export const enhancedBatchScrapingService = new EnhancedBatchScrapingService();

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * Format progress percentage for display
 */
export const formatProgressPercentage = (percentage: number): string => {
  return `${Math.round(percentage)}%`;
};

/**
 * Format batch scraping status for Vietnamese display
 */
export const formatBatchStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: 'Đang chờ',
    running: 'Đang chạy',
    completed: 'Hoàn thành',
    failed: 'Thất bại',
    cancelled: 'Đã hủy'
  };
  
  return statusMap[status] || status;
};

/**
 * Get status color for UI display
 */
export const getBatchStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    pending: 'text-yellow-400',
    running: 'text-blue-400',
    completed: 'text-green-400',
    failed: 'text-red-400',
    cancelled: 'text-gray-400'
  };
  
  return colorMap[status] || 'text-gray-400';
};

/**
 * Calculate estimated completion time
 */
export const calculateEstimatedCompletion = (
  progress: BatchProgressResponse,
  startTime: Date
): Date | null => {
  if (progress.completed_chapters === 0 || progress.progress_percentage === 0) {
    return null;
  }
  
  const elapsed = Date.now() - startTime.getTime();
  const estimatedTotal = (elapsed / progress.progress_percentage) * 100;
  const remaining = estimatedTotal - elapsed;
  
  return new Date(Date.now() + remaining);
};