"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/stories/[storyId]/page",{

/***/ "(app-pages-browser)/./src/components/story/EnhancedChapterList.tsx":
/*!******************************************************!*\
  !*** ./src/components/story/EnhancedChapterList.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnhancedChapterList: function() { return /* binding */ EnhancedChapterList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _hooks_useChaptersCache__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../hooks/useChaptersCache */ \"(app-pages-browser)/./src/hooks/useChaptersCache.ts\");\n/* harmony import */ var _services_hierarchicalScrapeService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../services/hierarchicalScrapeService */ \"(app-pages-browser)/./src/services/hierarchicalScrapeService.ts\");\n/* harmony import */ var _services_enhancedBatchScrapingService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../services/enhancedBatchScrapingService */ \"(app-pages-browser)/./src/services/enhancedBatchScrapingService.ts\");\n/* harmony import */ var _ChapterFilterModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ChapterFilterModal */ \"(app-pages-browser)/./src/components/story/ChapterFilterModal.tsx\");\n/* harmony import */ var _ChapterPagination__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ChapterPagination */ \"(app-pages-browser)/./src/components/story/ChapterPagination.tsx\");\n/* harmony import */ var _utils_chapterUtils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../utils/chapterUtils */ \"(app-pages-browser)/./src/utils/chapterUtils.tsx\");\n/* harmony import */ var _ui_Toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ EnhancedChapterList,default auto */ \nvar _s = $RefreshSig$();\n/**\n * Enhanced Chapter List Component\n * \n * Features:\n * - Improved UI/UX with modern design\n * - Enhanced batch scraping with real-time progress\n * - Toast notifications for user feedback\n * - Background scraping with immediate chapter access\n * - Detailed error handling and retry mechanisms\n * - Vietnamese language support\n */ \n\n\n\n\n\n\n\n\n// ============================================================================\n// Enhanced Chapter List Component\n// ============================================================================\nconst EnhancedChapterList = (param)=>{\n    let { storyId, chapters: propChapters, onChapterUpdate } = param;\n    _s();\n    // ========================================================================\n    // Hooks and State Management\n    // ========================================================================\n    // Use chapters cache when chapters are not provided as props\n    const { chapters: cachedChapters, loading: chaptersLoading, totalChapters, currentPage: cachePage, totalPages: cachedTotalPages, loadChapters } = (0,_hooks_useChaptersCache__WEBPACK_IMPORTED_MODULE_3__.useChaptersCache)(storyId);\n    // Use prop chapters if provided, otherwise use cached chapters\n    const chapters = propChapters || cachedChapters;\n    const [selectedChapters, setSelectedChapters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [scrapingProgress, setScrapingProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [batchScrapingState, setBatchScrapingState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isActive: false,\n        progress: null\n    });\n    const [popup, setPopup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showBatchControls, setShowBatchControls] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [itemsPerPage, setItemsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(50);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // Toast notifications\n    const { toasts, removeToast, showSuccess, showError, showWarning, showInfo, showProgress, updateToast } = (0,_ui_Toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    // Load chapters if not provided as props\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!propChapters && !chaptersLoading) {\n            loadChapters(currentPage, itemsPerPage);\n        }\n    }, [\n        propChapters,\n        chaptersLoading,\n        currentPage,\n        itemsPerPage,\n        loadChapters\n    ]);\n    // ========================================================================\n    // Computed Values\n    // ========================================================================\n    const filteredChapters = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (!chapters || chapters.length === 0) return [];\n        let filtered = chapters;\n        // Apply filters\n        if (filter.scraped !== null && filter.scraped !== undefined) {\n            filtered = filtered.filter((chapter)=>{\n                return chapter.is_scraped === filter.scraped;\n            });\n        }\n        if (filter.enhanced !== null && filter.enhanced !== undefined) {\n            filtered = filtered.filter((chapter)=>{\n                return chapter.is_enhanced === filter.enhanced;\n            });\n        }\n        if (filter.search) {\n            const searchTerm = filter.search.toLowerCase();\n            filtered = filtered.filter((chapter)=>chapter.title.toLowerCase().includes(searchTerm) || chapter.chapter_number.toString().includes(searchTerm));\n        }\n        return filtered.sort((a, b)=>a.chapter_number - b.chapter_number);\n    }, [\n        chapters,\n        filter\n    ]);\n    const paginatedChapters = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const startIndex = (currentPage - 1) * itemsPerPage;\n        return filteredChapters.slice(startIndex, startIndex + itemsPerPage);\n    }, [\n        filteredChapters,\n        currentPage,\n        itemsPerPage\n    ]);\n    const calculatedTotalPages = Math.ceil(filteredChapters.length / itemsPerPage);\n    const scrapingStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const total = filteredChapters.length;\n        const scraped = filteredChapters.filter((ch)=>ch.is_scraped).length;\n        const unscraped = total - scraped;\n        const selected = selectedChapters.size;\n        return {\n            total,\n            scraped,\n            unscraped,\n            selected\n        };\n    }, [\n        filteredChapters,\n        selectedChapters\n    ]);\n    // ========================================================================\n    // Effects\n    // ========================================================================\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Clear selected chapters when filter changes\n        setSelectedChapters(new Set());\n        setCurrentPage(1);\n    }, [\n        filter\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Cleanup batch scraping service on unmount\n        return ()=>{\n            _services_enhancedBatchScrapingService__WEBPACK_IMPORTED_MODULE_5__.enhancedBatchScrapingService.cleanup();\n        };\n    }, []);\n    // ========================================================================\n    // Individual Chapter Scraping\n    // ========================================================================\n    const handleScrapeChapter = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (chapter)=>{\n        const chapterId = chapter.id;\n        // Update progress\n        setScrapingProgress((prev)=>({\n                ...prev,\n                [chapterId]: {\n                    status: \"scraping\"\n                }\n            }));\n        try {\n            const scrapedChapter = await (0,_services_hierarchicalScrapeService__WEBPACK_IMPORTED_MODULE_4__.scrapeChapterContent)(chapterId);\n            // Update progress\n            setScrapingProgress((prev)=>({\n                    ...prev,\n                    [chapterId]: {\n                        status: \"completed\"\n                    }\n                }));\n            // Update chapter with scraped content\n            const updatedChapter = {\n                ...chapter,\n                original_content: scrapedChapter.content,\n                is_scraped: true,\n                updated_at: new Date().toISOString()\n            };\n            // Notify parent\n            if (onChapterUpdate) {\n                onChapterUpdate(updatedChapter);\n            }\n            // Show success toast\n            showSuccess(\"C\\xe0o chương th\\xe0nh c\\xf4ng\", \"Chương \".concat(chapter.chapter_number, \": \").concat((0,_utils_chapterUtils__WEBPACK_IMPORTED_MODULE_8__.cleanChapterTitle)(chapter.title, chapter.chapter_number)));\n            // Close popup if open\n            setPopup(null);\n        } catch (error) {\n            console.error(\"Error scraping chapter:\", error);\n            const currentProgress = scrapingProgress[chapterId];\n            const retryCount = ((currentProgress === null || currentProgress === void 0 ? void 0 : currentProgress.retryCount) || 0) + 1;\n            const maxRetries = 3;\n            if (retryCount < maxRetries) {\n                // Auto-retry\n                setScrapingProgress((prev)=>({\n                        ...prev,\n                        [chapterId]: {\n                            status: \"pending\",\n                            error: error.message,\n                            retryCount\n                        }\n                    }));\n                showWarning(\"Đang thử lại\", \"Chương \".concat(chapter.chapter_number, \" - Lần thử \").concat(retryCount, \"/\").concat(maxRetries));\n                // Retry after delay\n                setTimeout(()=>{\n                    handleScrapeChapter(chapter);\n                }, 2000 * retryCount);\n            } else {\n                // Max retries reached\n                setScrapingProgress((prev)=>({\n                        ...prev,\n                        [chapterId]: {\n                            status: \"failed\",\n                            error: error.message,\n                            retryCount\n                        }\n                    }));\n                showError(\"C\\xe0o chương thất bại\", \"Chương \".concat(chapter.chapter_number, \": \").concat(error.message), 8000);\n            }\n        }\n    }, [\n        scrapingProgress,\n        onChapterUpdate,\n        showSuccess,\n        showWarning,\n        showError\n    ]);\n    // ========================================================================\n    // Chapter Selection\n    // ========================================================================\n    const handleSelectChapter = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((chapterId)=>{\n        setSelectedChapters((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(chapterId)) {\n                newSet.delete(chapterId);\n            } else {\n                newSet.add(chapterId);\n            }\n            return newSet;\n        });\n    }, []);\n    const handleSelectAll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const unscrapedChapters = paginatedChapters.filter((ch)=>!ch.is_scraped).map((ch)=>ch.id);\n        setSelectedChapters(new Set(unscrapedChapters));\n    }, [\n        paginatedChapters\n    ]);\n    const handleDeselectAll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setSelectedChapters(new Set());\n    }, []);\n    // ========================================================================\n    // Enhanced Batch Scraping\n    // ========================================================================\n    const handleBatchScrape = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (selectedChapters.size === 0) {\n            showWarning(\"Chưa chọn chương\", \"Vui l\\xf2ng chọn \\xedt nhất một chương để c\\xe0o.\");\n            return;\n        }\n        const chapterIds = Array.from(selectedChapters);\n        const startTime = new Date();\n        // Initialize batch scraping state\n        setBatchScrapingState({\n            isActive: true,\n            progress: null,\n            startTime\n        });\n        // Show initial progress toast\n        const progressToastId = showProgress(\"Bắt đầu c\\xe0o h\\xe0ng loạt\", \"Đang c\\xe0o \".concat(chapterIds.length, \" chương...\"), 0);\n        try {\n            // Start batch scraping with progress tracking\n            const jobId = await _services_enhancedBatchScrapingService__WEBPACK_IMPORTED_MODULE_5__.enhancedBatchScrapingService.batchScrapeWithProgress(chapterIds, {\n                maxConcurrent: 10,\n                rateLimitDelay: 2.0,\n                onProgress: (progress)=>{\n                    // Update batch scraping state\n                    setBatchScrapingState((prev)=>({\n                            ...prev,\n                            progress,\n                            jobId: progress.job_id\n                        }));\n                    // Update progress toast\n                    updateToast(progressToastId, {\n                        title: \"Đang c\\xe0o h\\xe0ng loạt\",\n                        message: \"\".concat(progress.completed_chapters, \"/\").concat(progress.total_chapters, \" chương ho\\xe0n th\\xe0nh\"),\n                        progress: progress.progress_percentage\n                    });\n                    // Update individual chapter progress\n                    const newProgress = {};\n                    progress.results.forEach((result)=>{\n                        newProgress[result.chapter_id] = {\n                            status: result.status === \"success\" ? \"completed\" : result.status === \"failed\" ? \"failed\" : \"pending\",\n                            error: result.error_message\n                        };\n                    });\n                    setScrapingProgress((prev)=>({\n                            ...prev,\n                            ...newProgress\n                        }));\n                    // Update cached chapters for completed ones\n                    progress.results.filter((result)=>result.status === \"success\").forEach((result)=>{\n                        const chapter = cachedChapters.find((ch)=>ch.id === result.chapter_id);\n                        if (chapter) {\n                        // Trigger a refresh of the chapter data\n                        // This would typically involve fetching the updated chapter from the API\n                        // For now, we'll mark it as needing refresh\n                        // TODO: Implement proper chapter refresh\n                        }\n                    });\n                },\n                onComplete: (results)=>{\n                    // Batch scraping completed\n                    setBatchScrapingState({\n                        isActive: false,\n                        progress: null\n                    });\n                    // Remove progress toast and show completion toast\n                    removeToast(progressToastId);\n                    if (results.successful_count > 0) {\n                        showSuccess(\"C\\xe0o h\\xe0ng loạt ho\\xe0n th\\xe0nh\", \"Đ\\xe3 c\\xe0o th\\xe0nh c\\xf4ng \".concat(results.successful_count, \"/\").concat(results.total_requested, \" chương\"), 10000);\n                    }\n                    if (results.failed_count > 0) {\n                        showError(\"Một số chương c\\xe0o thất bại\", \"\".concat(results.failed_count, \" chương kh\\xf4ng thể c\\xe0o được. Xem chi tiết b\\xean dưới.\"), 10000);\n                    }\n                    // Clear selected chapters\n                    setSelectedChapters(new Set());\n                },\n                onError: (error)=>{\n                    setBatchScrapingState({\n                        isActive: false,\n                        progress: null\n                    });\n                    removeToast(progressToastId);\n                    showError(\"Lỗi c\\xe0o h\\xe0ng loạt\", error.message, 10000);\n                }\n            });\n            if (jobId) {\n                setBatchScrapingState((prev)=>({\n                        ...prev,\n                        jobId\n                    }));\n            }\n        } catch (error) {\n            setBatchScrapingState({\n                isActive: false,\n                progress: null\n            });\n            removeToast(progressToastId);\n            showError(\"Kh\\xf4ng thể bắt đầu c\\xe0o h\\xe0ng loạt\", error.message, 8000);\n        }\n    }, [\n        selectedChapters,\n        cachedChapters,\n        showProgress,\n        showSuccess,\n        showError,\n        showWarning,\n        updateToast,\n        removeToast\n    ]);\n    const handleCancelBatchScraping = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (batchScrapingState.jobId) {\n            try {\n                await _services_enhancedBatchScrapingService__WEBPACK_IMPORTED_MODULE_5__.enhancedBatchScrapingService.cancelBatchJob(batchScrapingState.jobId);\n                showInfo(\"Đ\\xe3 hủy c\\xe0o h\\xe0ng loạt\", \"Qu\\xe1 tr\\xecnh c\\xe0o h\\xe0ng loạt đ\\xe3 được hủy.\");\n            } catch (error) {\n                showError(\"Kh\\xf4ng thể hủy\", error.message);\n            }\n        }\n        setBatchScrapingState({\n            isActive: false,\n            progress: null\n        });\n    }, [\n        batchScrapingState.jobId,\n        showInfo,\n        showError\n    ]);\n    // ========================================================================\n    // Progress Indicators\n    // ========================================================================\n    const getProgressIndicator = (chapterId)=>{\n        const progress = scrapingProgress[chapterId];\n        if (!progress) return null;\n        const iconClass = \"w-4 h-4\";\n        switch(progress.status){\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center w-6 h-6 bg-yellow-100 rounded-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-3 h-3 bg-yellow-400 rounded-full animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                    lineNumber: 466,\n                    columnNumber: 11\n                }, undefined);\n            case \"scraping\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center w-6 h-6 bg-blue-100 rounded-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"\".concat(iconClass, \" text-blue-600 animate-spin\"),\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                className: \"opacity-25\",\n                                cx: \"12\",\n                                cy: \"12\",\n                                r: \"10\",\n                                stroke: \"currentColor\",\n                                strokeWidth: \"4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                className: \"opacity-75\",\n                                fill: \"currentColor\",\n                                d: \"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                        lineNumber: 473,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                    lineNumber: 472,\n                    columnNumber: 11\n                }, undefined);\n            case \"completed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center w-6 h-6 bg-green-100 rounded-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"\".concat(iconClass, \" text-green-600\"),\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 20 20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            fillRule: \"evenodd\",\n                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                            clipRule: \"evenodd\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                            lineNumber: 483,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                        lineNumber: 482,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                    lineNumber: 481,\n                    columnNumber: 11\n                }, undefined);\n            case \"failed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center w-6 h-6 bg-red-100 rounded-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"\".concat(iconClass, \" text-red-600\"),\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 20 20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            fillRule: \"evenodd\",\n                            d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                            clipRule: \"evenodd\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                            lineNumber: 491,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                        lineNumber: 490,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                    lineNumber: 489,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    // ========================================================================\n    // Render Methods\n    // ========================================================================\n    const renderBatchControls = ()=>{\n        if (!showBatchControls && selectedChapters.size === 0) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-blue-900\",\n                                    children: \"C\\xe0o h\\xe0ng loạt\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-blue-700\",\n                                    children: [\n                                        \"Đ\\xe3 chọn: \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: selectedChapters.size\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 24\n                                        }, undefined),\n                                        \" chương\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowBatchControls(!showBatchControls),\n                            className: \"text-blue-600 hover:text-blue-800 transition-colors\",\n                            children: [\n                                showBatchControls ? \"Ẩn\" : \"Hiện\",\n                                \" điều khiển\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                            lineNumber: 517,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                    lineNumber: 509,\n                    columnNumber: 9\n                }, undefined),\n                showBatchControls && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSelectAll,\n                                    className: \"px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors\",\n                                    children: \"Chọn tất cả (chưa c\\xe0o)\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                    lineNumber: 529,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleDeselectAll,\n                                    className: \"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors\",\n                                    children: \"Bỏ chọn tất cả\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                            lineNumber: 528,\n                            columnNumber: 13\n                        }, undefined),\n                        batchScrapingState.isActive && batchScrapingState.progress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg p-3 border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Tiến độ c\\xe0o h\\xe0ng loạt\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                batchScrapingState.progress.completed_chapters,\n                                                \"/\",\n                                                batchScrapingState.progress.total_chapters\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full bg-gray-200 rounded-full h-2 mb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                        style: {\n                                            width: \"\".concat(batchScrapingState.progress.progress_percentage, \"%\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                    lineNumber: 552,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: batchScrapingState.progress.current_chapter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Đang c\\xe0o: \",\n                                            batchScrapingState.progress.current_chapter\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                    lineNumber: 558,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                            lineNumber: 545,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: !batchScrapingState.isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleBatchScrape,\n                                disabled: selectedChapters.size === 0,\n                                className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors\",\n                                children: [\n                                    \"Bắt đầu c\\xe0o (\",\n                                    selectedChapters.size,\n                                    \" chương)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                lineNumber: 569,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCancelBatchScraping,\n                                className: \"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\",\n                                children: \"Hủy c\\xe0o h\\xe0ng loạt\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                lineNumber: 577,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                            lineNumber: 567,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                    lineNumber: 526,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n            lineNumber: 508,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderStatsBar = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-slate-800 border border-slate-700 rounded-lg p-4 mb-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: scrapingStats.total\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-slate-400\",\n                                children: \"Tổng chương\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                lineNumber: 596,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                        lineNumber: 594,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-green-400\",\n                                children: scrapingStats.scraped\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                lineNumber: 599,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-slate-400\",\n                                children: \"Đ\\xe3 c\\xe0o\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                lineNumber: 600,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                        lineNumber: 598,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-orange-400\",\n                                children: scrapingStats.unscraped\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-slate-400\",\n                                children: \"Chưa c\\xe0o\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                lineNumber: 604,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                        lineNumber: 602,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-blue-400\",\n                                children: scrapingStats.selected\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                lineNumber: 607,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-slate-400\",\n                                children: \"Đ\\xe3 chọn\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                lineNumber: 608,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                        lineNumber: 606,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                lineNumber: 593,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n            lineNumber: 592,\n            columnNumber: 5\n        }, undefined);\n    // ========================================================================\n    // Main Render\n    // ========================================================================\n    if (chaptersLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-12 bg-slate-900 min-h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                    lineNumber: 621,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-3 text-slate-300\",\n                    children: \"Đang tải danh s\\xe1ch chương...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                    lineNumber: 622,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n            lineNumber: 620,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 bg-slate-900 min-h-screen p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Toast__WEBPACK_IMPORTED_MODULE_9__.ToastContainer, {\n                toasts: toasts,\n                onDismiss: removeToast,\n                position: \"top-right\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                lineNumber: 630,\n                columnNumber: 7\n            }, undefined),\n            renderStatsBar(),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChapterFilterModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                filter: filter,\n                onFilterChange: setFilter,\n                stats: scrapingStats\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                lineNumber: 636,\n                columnNumber: 7\n            }, undefined),\n            renderBatchControls(),\n            errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-5 h-5 text-red-400 mr-3\",\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 20 20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                fillRule: \"evenodd\",\n                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                clipRule: \"evenodd\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                lineNumber: 650,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                            lineNumber: 649,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-800\",\n                            children: errorMessage\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                            lineNumber: 652,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                    lineNumber: 648,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                lineNumber: 647,\n                columnNumber: 9\n            }, undefined),\n            paginatedChapters.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-slate-400 text-lg mb-2\",\n                        children: \"Kh\\xf4ng t\\xecm thấy chương n\\xe0o\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                        lineNumber: 660,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-slate-500 text-sm\",\n                        children: \"Thử thay đổi bộ lọc hoặc t\\xecm kiếm\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                        lineNumber: 661,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                lineNumber: 659,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                children: paginatedChapters.map((chapter)=>{\n                    var _scrapingProgress_chapter_id, _scrapingProgress_chapter_id1, _scrapingProgress_chapter_id2, _scrapingProgress_chapter_id3;\n                    const hasContent = chapter.original_content && chapter.original_content.trim().length > 0;\n                    const isSelected = selectedChapters.has(chapter.id);\n                    const progressIndicator = getProgressIndicator(chapter.id);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"group relative bg-slate-800 border border-slate-700 rounded-lg p-3 transition-all duration-200 hover:bg-slate-750 hover:border-slate-600 \".concat(isSelected ? \"border-blue-500 bg-slate-750\" : \"\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            !hasContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: isSelected,\n                                                onChange: ()=>handleSelectChapter(chapter.id),\n                                                className: \"w-4 h-4 text-blue-600 bg-slate-700 border-slate-600 rounded focus:ring-blue-500 focus:ring-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs font-medium text-slate-400\",\n                                                children: [\n                                                    \"Chương \",\n                                                    chapter.chapter_number,\n                                                    \":\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                                lineNumber: 688,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                        lineNumber: 678,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            (0,_utils_chapterUtils__WEBPACK_IMPORTED_MODULE_8__.getChapterStatusBadge)(chapter),\n                                            progressIndicator\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                        lineNumber: 693,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                lineNumber: 677,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-2\",\n                                children: hasContent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/story/\".concat(storyId, \"/chapter/\").concat(chapter.id),\n                                    className: \"block text-white hover:text-blue-400 font-medium text-sm transition-colors line-clamp-2\",\n                                    children: (0,_utils_chapterUtils__WEBPACK_IMPORTED_MODULE_8__.cleanChapterTitle)(chapter.title, chapter.chapter_number)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                    lineNumber: 702,\n                                    columnNumber: 21\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    onClick: ()=>setPopup({\n                                            chapterId: chapter.id,\n                                            chapterNumber: chapter.chapter_number\n                                        }),\n                                    className: \"cursor-pointer text-slate-300 hover:text-white font-medium text-sm transition-colors line-clamp-2\",\n                                    children: (0,_utils_chapterUtils__WEBPACK_IMPORTED_MODULE_8__.cleanChapterTitle)(chapter.title, chapter.chapter_number)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                    lineNumber: 709,\n                                    columnNumber: 21\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                lineNumber: 700,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between text-xs text-slate-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: chapter.updated_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: new Date(chapter.updated_at).toLocaleDateString(\"vi-VN\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                            lineNumber: 722,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                        lineNumber: 720,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    !hasContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleScrapeChapter(chapter),\n                                        disabled: ((_scrapingProgress_chapter_id = scrapingProgress[chapter.id]) === null || _scrapingProgress_chapter_id === void 0 ? void 0 : _scrapingProgress_chapter_id.status) === \"scraping\",\n                                        className: \"px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                        children: ((_scrapingProgress_chapter_id1 = scrapingProgress[chapter.id]) === null || _scrapingProgress_chapter_id1 === void 0 ? void 0 : _scrapingProgress_chapter_id1.status) === \"scraping\" ? \"Đang c\\xe0o...\" : \"C\\xe0o ngay\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                        lineNumber: 728,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                lineNumber: 719,\n                                columnNumber: 17\n                            }, undefined),\n                            ((_scrapingProgress_chapter_id2 = scrapingProgress[chapter.id]) === null || _scrapingProgress_chapter_id2 === void 0 ? void 0 : _scrapingProgress_chapter_id2.status) === \"failed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 p-2 bg-red-900/50 border border-red-700 rounded text-xs\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-red-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Lỗi:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                                    lineNumber: 743,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                \" \",\n                                                (_scrapingProgress_chapter_id3 = scrapingProgress[chapter.id]) === null || _scrapingProgress_chapter_id3 === void 0 ? void 0 : _scrapingProgress_chapter_id3.error\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                            lineNumber: 742,\n                                            columnNumber: 23\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleScrapeChapter(chapter),\n                                            className: \"px-2 py-1 bg-red-700 text-red-100 rounded hover:bg-red-600 transition-colors\",\n                                            children: \"Thử lại\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                            lineNumber: 745,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                    lineNumber: 741,\n                                    columnNumber: 21\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                lineNumber: 740,\n                                columnNumber: 19\n                            }, undefined)\n                        ]\n                    }, chapter.id, true, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                        lineNumber: 671,\n                        columnNumber: 15\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                lineNumber: 664,\n                columnNumber: 9\n            }, undefined),\n            paginatedChapters.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChapterPagination__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                currentPage: currentPage,\n                totalPages: calculatedTotalPages,\n                totalItems: filteredChapters.length,\n                onPageChange: setCurrentPage,\n                itemsPerPage: itemsPerPage,\n                onItemsPerPageChange: setItemsPerPage\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                lineNumber: 762,\n                columnNumber: 9\n            }, undefined),\n            popup && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 max-w-md w-full mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: [\n                                \"Chương \",\n                                popup.chapterNumber,\n                                \" chưa được c\\xe0o\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                            lineNumber: 776,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"Chương n\\xe0y chưa c\\xf3 nội dung. Bạn c\\xf3 muốn c\\xe0o ngay b\\xe2y giờ kh\\xf4ng?\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                            lineNumber: 779,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3 justify-end\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setPopup(null),\n                                    className: \"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                    children: \"Hủy\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                    lineNumber: 783,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        const chapter = chapters.find((ch)=>ch.id === popup.chapterId);\n                                        if (chapter) {\n                                            handleScrapeChapter(chapter);\n                                        }\n                                    },\n                                    className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                    children: \"C\\xe0o ngay\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                                    lineNumber: 789,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                            lineNumber: 782,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                    lineNumber: 775,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n                lineNumber: 774,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\EnhancedChapterList.tsx\",\n        lineNumber: 628,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedChapterList, \"QoScS/QNl5WUuNmj8B4gaktNkv0=\", false, function() {\n    return [\n        _hooks_useChaptersCache__WEBPACK_IMPORTED_MODULE_3__.useChaptersCache,\n        _ui_Toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = EnhancedChapterList;\n/* harmony default export */ __webpack_exports__[\"default\"] = (EnhancedChapterList);\nvar _c;\n$RefreshReg$(_c, \"EnhancedChapterList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/story/EnhancedChapterList.tsx\n"));

/***/ })

});