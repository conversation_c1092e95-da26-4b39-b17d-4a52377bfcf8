"""Enhanced Batch Chapter Scraping API

Provides advanced batch scraping functionality with:
- Specific chapter ID targeting
- Real-time progress tracking
- Detailed error reporting
- Rate limiting and concurrency control
- Background processing support
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Request
from pydantic import BaseModel, Field
from bson import ObjectId

from API.models.database import db_manager
from API.services.hierarchical_scraping_service import HierarchicalScrapingService
from API.core.config import get_settings

router = APIRouter(prefix="/batch-scraping", tags=["batch-scraping"])

# ============================================================================
# Request/Response Models
# ============================================================================

class BatchScrapeRequest(BaseModel):
    """Request model for batch chapter scraping"""
    chapter_ids: List[str] = Field(..., description="List of chapter IDs to scrape")
    max_concurrent: int = Field(default=3, ge=1, le=10, description="Maximum concurrent scraping operations")
    rate_limit_delay: float = Field(default=2.0, ge=0.5, le=10.0, description="Delay between requests in seconds")
    background: bool = Field(default=False, description="Run scraping in background")

class ChapterScrapeResult(BaseModel):
    """Result for individual chapter scraping"""
    chapter_id: str
    chapter_number: int
    status: str  # 'success', 'failed', 'skipped'
    error_message: Optional[str] = None
    scraped_at: Optional[datetime] = None
    word_count: Optional[int] = None

class BatchScrapeResponse(BaseModel):
    """Response model for batch scraping"""
    job_id: Optional[str] = None
    success: bool
    message: str
    total_requested: int
    successful_count: int
    failed_count: int
    skipped_count: int
    results: List[ChapterScrapeResult]
    started_at: datetime
    completed_at: Optional[datetime] = None
    estimated_completion: Optional[datetime] = None

class BatchProgressResponse(BaseModel):
    """Progress tracking response"""
    job_id: str
    status: str  # 'pending', 'running', 'completed', 'failed'
    progress_percentage: float
    total_chapters: int
    completed_chapters: int
    failed_chapters: int
    current_chapter: Optional[str] = None
    estimated_completion: Optional[datetime] = None
    results: List[ChapterScrapeResult]

# ============================================================================
# Service Class
# ============================================================================

class EnhancedBatchScrapingService:
    """Enhanced service for batch chapter scraping with progress tracking"""
    
    def __init__(self):
        self.db = db_manager.database
        self.hierarchical_service = HierarchicalScrapingService()
        self.active_jobs: Dict[str, Dict[str, Any]] = {}
    
    async def validate_chapters(self, chapter_ids: List[str]) -> Dict[str, Any]:
        """Validate chapter IDs and return chapter information"""
        valid_chapters = []
        invalid_ids = []
        already_scraped = []
        
        for chapter_id in chapter_ids:
            try:
                # Convert to ObjectId
                chapter_object_id = ObjectId(chapter_id)
                
                # Find chapter in database
                chapter = await self.db.chapters.find_one({"_id": chapter_object_id})
                
                if not chapter:
                    invalid_ids.append(chapter_id)
                    continue
                
                if chapter.get('is_scraped', False):
                    already_scraped.append({
                        'id': chapter_id,
                        'chapter_number': chapter.get('chapter_number', 0)
                    })
                else:
                    valid_chapters.append({
                        'id': chapter_id,
                        'chapter_number': chapter.get('chapter_number', 0),
                        'title': chapter.get('title', ''),
                        'story_id': chapter.get('story_id')
                    })
                    
            except Exception as e:
                self.log_error(f"Error validating chapter {chapter_id}: {str(e)}")
                invalid_ids.append(chapter_id)
        
        return {
            'valid_chapters': valid_chapters,
            'invalid_ids': invalid_ids,
            'already_scraped': already_scraped
        }
    
    async def scrape_chapters_batch(
        self,
        chapter_ids: List[str],
        max_concurrent: int = 3,
        rate_limit_delay: float = 2.0,
        request: Request = None,
        job_id: Optional[str] = None
    ) -> BatchScrapeResponse:
        """Scrape multiple chapters with enhanced progress tracking"""
        
        started_at = datetime.utcnow()
        
        # Validate chapters
        validation_result = await self.validate_chapters(chapter_ids)
        valid_chapters = validation_result['valid_chapters']
        invalid_ids = validation_result['invalid_ids']
        already_scraped = validation_result['already_scraped']
        
        # Initialize results
        results = []
        
        # Add skipped results for invalid and already scraped chapters
        for invalid_id in invalid_ids:
            results.append(ChapterScrapeResult(
                chapter_id=invalid_id,
                chapter_number=0,
                status='failed',
                error_message='Chapter not found in database'
            ))
        
        for scraped_chapter in already_scraped:
            results.append(ChapterScrapeResult(
                chapter_id=scraped_chapter['id'],
                chapter_number=scraped_chapter['chapter_number'],
                status='skipped',
                error_message='Chapter already scraped'
            ))
        
        if not valid_chapters:
            return BatchScrapeResponse(
                job_id=job_id,
                success=True,
                message="No chapters to scrape",
                total_requested=len(chapter_ids),
                successful_count=0,
                failed_count=len(invalid_ids),
                skipped_count=len(already_scraped),
                results=results,
                started_at=started_at,
                completed_at=datetime.utcnow()
            )
        
        # Initialize job tracking if job_id provided
        if job_id:
            self.active_jobs[job_id] = {
                'status': 'running',
                'total_chapters': len(valid_chapters),
                'completed_chapters': 0,
                'failed_chapters': 0,
                'results': results.copy(),
                'started_at': started_at
            }
        
        # Process chapters with concurrency control
        semaphore = asyncio.Semaphore(max_concurrent)
        successful_count = 0
        failed_count = len(invalid_ids)
        
        async def scrape_single_chapter(chapter_info: Dict[str, Any]) -> ChapterScrapeResult:
            nonlocal successful_count, failed_count
            
            async with semaphore:
                try:
                    # Rate limiting
                    await asyncio.sleep(rate_limit_delay)
                    
                    # Update job progress
                    if job_id:
                        self.active_jobs[job_id]['current_chapter'] = f"Chapter {chapter_info['chapter_number']}"
                    
                    # Scrape chapter content
                    result = await self.hierarchical_service.scrape_chapter_content(
                        chapter_info['id'], request
                    )
                    
                    successful_count += 1
                    
                    # Update job progress
                    if job_id:
                        self.active_jobs[job_id]['completed_chapters'] += 1
                    
                    return ChapterScrapeResult(
                        chapter_id=chapter_info['id'],
                        chapter_number=chapter_info['chapter_number'],
                        status='success',
                        scraped_at=datetime.utcnow(),
                        word_count=result.get('word_count')
                    )
                    
                except Exception as e:
                    self.log_error(f"Error scraping chapter {chapter_info['id']}: {str(e)}")
                    failed_count += 1
                    
                    # Update job progress
                    if job_id:
                        self.active_jobs[job_id]['failed_chapters'] += 1
                    
                    return ChapterScrapeResult(
                        chapter_id=chapter_info['id'],
                        chapter_number=chapter_info['chapter_number'],
                        status='failed',
                        error_message=str(e)
                    )
        
        # Execute scraping tasks
        tasks = [scrape_single_chapter(chapter) for chapter in valid_chapters]
        scraping_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        for result in scraping_results:
            if isinstance(result, Exception):
                failed_count += 1
                results.append(ChapterScrapeResult(
                    chapter_id="unknown",
                    chapter_number=0,
                    status='failed',
                    error_message=str(result)
                ))
            else:
                results.append(result)
        
        completed_at = datetime.utcnow()
        
        # Update job status
        if job_id:
            self.active_jobs[job_id].update({
                'status': 'completed',
                'completed_at': completed_at,
                'results': results
            })
        
        return BatchScrapeResponse(
            job_id=job_id,
            success=True,
            message=f"Batch scraping completed: {successful_count} successful, {failed_count} failed",
            total_requested=len(chapter_ids),
            successful_count=successful_count,
            failed_count=failed_count,
            skipped_count=len(already_scraped),
            results=results,
            started_at=started_at,
            completed_at=completed_at
        )
    
    def get_job_progress(self, job_id: str) -> Optional[BatchProgressResponse]:
        """Get progress for a specific job"""
        if job_id not in self.active_jobs:
            return None
        
        job = self.active_jobs[job_id]
        total = job['total_chapters']
        completed = job['completed_chapters']
        
        progress_percentage = (completed / total * 100) if total > 0 else 0
        
        return BatchProgressResponse(
            job_id=job_id,
            status=job['status'],
            progress_percentage=progress_percentage,
            total_chapters=total,
            completed_chapters=completed,
            failed_chapters=job['failed_chapters'],
            current_chapter=job.get('current_chapter'),
            results=job['results']
        )

# ============================================================================
# Service Instance
# ============================================================================

batch_scraping_service = EnhancedBatchScrapingService()

def get_batch_scraping_service() -> EnhancedBatchScrapingService:
    return batch_scraping_service

# ============================================================================
# API Endpoints
# ============================================================================

@router.post("/chapters", response_model=BatchScrapeResponse)
async def batch_scrape_chapters(
    request_data: BatchScrapeRequest,
    background_tasks: BackgroundTasks,
    request: Request,
    service: EnhancedBatchScrapingService = Depends(get_batch_scraping_service)
) -> BatchScrapeResponse:
    """Scrape multiple chapters by their IDs"""
    
    if not request_data.chapter_ids:
        raise HTTPException(status_code=400, detail="No chapter IDs provided")
    
    if len(request_data.chapter_ids) > 100:
        raise HTTPException(status_code=400, detail="Too many chapters requested (max 100)")
    
    # Generate job ID for tracking
    job_id = f"batch_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{len(request_data.chapter_ids)}"
    
    if request_data.background:
        # Run in background
        background_tasks.add_task(
            service.scrape_chapters_batch,
            request_data.chapter_ids,
            request_data.max_concurrent,
            request_data.rate_limit_delay,
            request,
            job_id
        )
        
        return BatchScrapeResponse(
            job_id=job_id,
            success=True,
            message="Batch scraping started in background",
            total_requested=len(request_data.chapter_ids),
            successful_count=0,
            failed_count=0,
            skipped_count=0,
            results=[],
            started_at=datetime.utcnow()
        )
    else:
        # Run synchronously
        return await service.scrape_chapters_batch(
            request_data.chapter_ids,
            request_data.max_concurrent,
            request_data.rate_limit_delay,
            request,
            job_id
        )

@router.get("/progress/{job_id}", response_model=BatchProgressResponse)
async def get_batch_progress(
    job_id: str,
    service: EnhancedBatchScrapingService = Depends(get_batch_scraping_service)
) -> BatchProgressResponse:
    """Get progress for a batch scraping job"""
    
    progress = service.get_job_progress(job_id)
    if not progress:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return progress

@router.delete("/jobs/{job_id}")
async def cancel_batch_job(
    job_id: str,
    service: EnhancedBatchScrapingService = Depends(get_batch_scraping_service)
) -> Dict[str, str]:
    """Cancel a running batch job"""
    
    if job_id not in service.active_jobs:
        raise HTTPException(status_code=404, detail="Job not found")
    
    # Mark job as cancelled
    service.active_jobs[job_id]['status'] = 'cancelled'
    
    return {"message": f"Job {job_id} cancelled successfully"}

@router.get("/jobs", response_model=List[BatchProgressResponse])
async def list_active_jobs(
    service: EnhancedBatchScrapingService = Depends(get_batch_scraping_service)
) -> List[BatchProgressResponse]:
    """List all active batch scraping jobs"""
    
    jobs = []
    for job_id in service.active_jobs:
        progress = service.get_job_progress(job_id)
        if progress:
            jobs.append(progress)
    
    return jobs