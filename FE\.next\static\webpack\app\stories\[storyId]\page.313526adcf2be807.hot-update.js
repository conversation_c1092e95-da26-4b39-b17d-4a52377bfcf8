"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/stories/[storyId]/page",{

/***/ "(app-pages-browser)/./src/services/enhancedBatchScrapingService.ts":
/*!******************************************************!*\
  !*** ./src/services/enhancedBatchScrapingService.ts ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateEstimatedCompletion: function() { return /* binding */ calculateEstimatedCompletion; },\n/* harmony export */   enhancedBatchScrapingService: function() { return /* binding */ enhancedBatchScrapingService; },\n/* harmony export */   formatBatchStatus: function() { return /* binding */ formatBatchStatus; },\n/* harmony export */   formatProgressPercentage: function() { return /* binding */ formatProgressPercentage; },\n/* harmony export */   getBatchStatusColor: function() { return /* binding */ getBatchStatusColor; }\n/* harmony export */ });\n/**\n * Enhanced Batch Scraping Service\n * \n * Provides advanced batch scraping functionality with:\n * - Specific chapter ID targeting\n * - Real-time progress tracking\n * - Detailed error reporting\n * - Background processing support\n */ const API_BASE_URL = \"http://localhost:8002\" || 0;\nconst API_PREFIX = \"/api/v1/batch-scraping\";\n// Helper function to make API requests\nconst makeApiRequest = async function(endpoint) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const url = \"\".concat(API_BASE_URL).concat(API_PREFIX).concat(endpoint);\n    const response = await fetch(url, {\n        ...options,\n        headers: {\n            \"Content-Type\": \"application/json\",\n            ...options.headers\n        }\n    });\n    if (!response.ok) {\n        throw new Error(\"API request failed: \".concat(response.status, \" \").concat(response.statusText));\n    }\n    return response.json();\n};\n// ============================================================================\n// Enhanced Batch Scraping Service\n// ============================================================================\nclass EnhancedBatchScrapingService {\n    /**\n   * Start batch scraping for specific chapters\n   */ async startBatchScraping(chapterIds) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const request = {\n            chapter_ids: chapterIds,\n            max_concurrent: options.maxConcurrent || 3,\n            rate_limit_delay: options.rateLimitDelay || 2.0,\n            background: options.background || false\n        };\n        try {\n            const response = await makeApiRequest(\"/scrape-chapters\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(request)\n            });\n            // If background processing and we have a job ID, start polling\n            if (response.job_id && options.background && options.onProgress) {\n                this.startProgressPolling(response.job_id, options.onProgress);\n            }\n            return response;\n        } catch (error) {\n            console.error(\"Error starting batch scraping:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get progress for a specific batch job\n   */ async getBatchProgress(jobId) {\n        try {\n            const progress = await makeApiRequest(\"/progress/\".concat(jobId));\n            // Update local cache\n            this.activeJobs.set(jobId, progress);\n            return progress;\n        } catch (error) {\n            console.error(\"Error getting progress for job \".concat(jobId, \":\"), error);\n            throw error;\n        }\n    }\n    /**\n   * Start polling for progress updates\n   */ startProgressPolling(jobId, onProgress) {\n        let intervalMs = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 2000;\n        // Store callback\n        this.progressCallbacks.set(jobId, onProgress);\n        // Clear existing interval if any\n        const existingInterval = this.pollingIntervals.get(jobId);\n        if (existingInterval) {\n            clearInterval(existingInterval);\n        }\n        // Start polling\n        const interval = setInterval(async ()=>{\n            try {\n                const progress = await this.getBatchProgress(jobId);\n                // Call progress callback\n                onProgress(progress);\n                // Stop polling if job is completed, failed, or cancelled\n                if ([\n                    \"completed\",\n                    \"failed\",\n                    \"cancelled\"\n                ].includes(progress.status)) {\n                    this.stopProgressPolling(jobId);\n                }\n            } catch (error) {\n                console.error(\"Error polling progress for job \".concat(jobId, \":\"), error);\n            // Continue polling on error, but log it\n            }\n        }, intervalMs);\n        this.pollingIntervals.set(jobId, interval);\n    }\n    /**\n   * Stop polling for a specific job\n   */ stopProgressPolling(jobId) {\n        const interval = this.pollingIntervals.get(jobId);\n        if (interval) {\n            clearInterval(interval);\n            this.pollingIntervals.delete(jobId);\n        }\n        this.progressCallbacks.delete(jobId);\n    }\n    /**\n   * Cancel a running batch job\n   */ async cancelBatchJob(jobId) {\n        try {\n            const response = await makeApiRequest(\"/jobs/\".concat(jobId), {\n                method: \"DELETE\"\n            });\n            // Stop polling\n            this.stopProgressPolling(jobId);\n            return response;\n        } catch (error) {\n            console.error(\"Error cancelling job \".concat(jobId, \":\"), error);\n            throw error;\n        }\n    }\n    /**\n   * List all active batch jobs\n   */ async listActiveJobs() {\n        try {\n            return await makeApiRequest(\"/jobs\");\n        } catch (error) {\n            console.error(\"Error listing active jobs:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get cached progress for a job (without API call)\n   */ getCachedProgress(jobId) {\n        return this.activeJobs.get(jobId);\n    }\n    /**\n   * Clean up all polling intervals (call on component unmount)\n   */ cleanup() {\n        this.pollingIntervals.forEach((interval)=>clearInterval(interval));\n        this.pollingIntervals.clear();\n        this.progressCallbacks.clear();\n        this.activeJobs.clear();\n    }\n    /**\n   * Batch scrape with real-time progress tracking\n   * This is a convenience method that combines starting and tracking\n   */ async batchScrapeWithProgress(chapterIds) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        try {\n            // Start batch scraping in background\n            const response = await this.startBatchScraping(chapterIds, {\n                ...options,\n                background: true\n            });\n            if (!response.job_id) {\n                // Immediate completion (no background job)\n                if (options.onComplete) {\n                    options.onComplete(response);\n                }\n                return null;\n            }\n            // Set up progress tracking with completion handling\n            const originalOnProgress = options.onProgress;\n            const enhancedOnProgress = (progress)=>{\n                if (originalOnProgress) {\n                    originalOnProgress(progress);\n                }\n                // Check for completion\n                if (progress.status === \"completed\" && options.onComplete) {\n                    const completedResponse = {\n                        job_id: progress.job_id,\n                        success: true,\n                        message: \"Batch scraping completed\",\n                        total_requested: progress.total_chapters,\n                        successful_count: progress.completed_chapters,\n                        failed_count: progress.failed_chapters,\n                        skipped_count: 0,\n                        results: progress.results,\n                        started_at: response.started_at,\n                        completed_at: new Date().toISOString()\n                    };\n                    options.onComplete(completedResponse);\n                }\n            };\n            // Start progress polling\n            this.startProgressPolling(response.job_id, enhancedOnProgress);\n            return response.job_id;\n        } catch (error) {\n            if (options.onError) {\n                options.onError(error);\n            }\n            throw error;\n        }\n    }\n    constructor(){\n        this.activeJobs = new Map();\n        this.progressCallbacks = new Map();\n        this.pollingIntervals = new Map();\n    }\n}\n// ============================================================================\n// Service Instance\n// ============================================================================\nconst enhancedBatchScrapingService = new EnhancedBatchScrapingService();\n// ============================================================================\n// Utility Functions\n// ============================================================================\n/**\n * Format progress percentage for display\n */ const formatProgressPercentage = (percentage)=>{\n    return \"\".concat(Math.round(percentage), \"%\");\n};\n/**\n * Format batch scraping status for Vietnamese display\n */ const formatBatchStatus = (status)=>{\n    const statusMap = {\n        pending: \"Đang chờ\",\n        running: \"Đang chạy\",\n        completed: \"Ho\\xe0n th\\xe0nh\",\n        failed: \"Thất bại\",\n        cancelled: \"Đ\\xe3 hủy\"\n    };\n    return statusMap[status] || status;\n};\n/**\n * Get status color for UI display\n */ const getBatchStatusColor = (status)=>{\n    const colorMap = {\n        pending: \"text-yellow-400\",\n        running: \"text-blue-400\",\n        completed: \"text-green-400\",\n        failed: \"text-red-400\",\n        cancelled: \"text-gray-400\"\n    };\n    return colorMap[status] || \"text-gray-400\";\n};\n/**\n * Calculate estimated completion time\n */ const calculateEstimatedCompletion = (progress, startTime)=>{\n    if (progress.completed_chapters === 0 || progress.progress_percentage === 0) {\n        return null;\n    }\n    const elapsed = Date.now() - startTime.getTime();\n    const estimatedTotal = elapsed / progress.progress_percentage * 100;\n    const remaining = estimatedTotal - elapsed;\n    return new Date(Date.now() + remaining);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/enhancedBatchScrapingService.ts\n"));

/***/ })

});