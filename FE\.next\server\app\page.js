/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CPersonal%20Projects%5CVibe%5CWebtruyen%5CFE%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CPersonal%20Projects%5CVibe%5CWebtruyen%5CFE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CPersonal%20Projects%5CVibe%5CWebtruyen%5CFE%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CPersonal%20Projects%5CVibe%5CWebtruyen%5CFE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\layout.tsx\"],\n'loading': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/loading.tsx */ \"(rsc)/./src/app/loading.tsx\")), \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\loading.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\")), \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CPersonal%20Projects%5CVibe%5CWebtruyen%5CFE%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CPersonal%20Projects%5CVibe%5CWebtruyen%5CFE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQZXJzb25hbCUyMFByb2plY3RzJTVDJTVDVmliZSU1QyU1Q1dlYnRydXllbiU1QyU1Q0ZFJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNsaW5rLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyKiUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDUGVyc29uYWwlMjBQcm9qZWN0cyU1QyU1Q1ZpYmUlNUMlNUNXZWJ0cnV5ZW4lNUMlNUNGRSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDUGVyc29uYWwlMjBQcm9qZWN0cyU1QyU1Q1ZpYmUlNUMlNUNXZWJ0cnV5ZW4lNUMlNUNGRSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBMEgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mZS8/NWZlMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFBlcnNvbmFsIFByb2plY3RzXFxcXFZpYmVcXFxcV2VidHJ1eWVuXFxcXEZFXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGxpbmsuanNcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQZXJzb25hbCUyMFByb2plY3RzJTVDJTVDVmliZSU1QyU1Q1dlYnRydXllbiU1QyU1Q0ZFJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNsaW5rLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyKiUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ01BQTBIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmUvPzI4MWIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQZXJzb25hbCBQcm9qZWN0c1xcXFxWaWJlXFxcXFdlYnRydXllblxcXFxGRVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxsaW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Csrc%5C%5Ccomponents%5C%5Cstory%5C%5CStoryList.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Csrc%5C%5Ccomponents%5C%5Cstory%5C%5CStoryList.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/story/StoryList.tsx */ \"(ssr)/./src/components/story/StoryList.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQZXJzb25hbCUyMFByb2plY3RzJTVDJTVDVmliZSU1QyU1Q1dlYnRydXllbiU1QyU1Q0ZFJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q3N0b3J5JTVDJTVDU3RvcnlMaXN0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9MQUFtSiIsInNvdXJjZXMiOlsid2VicGFjazovL2ZlLz8wZmUzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkQ6XFxcXFBlcnNvbmFsIFByb2plY3RzXFxcXFZpYmVcXFxcV2VidHJ1eWVuXFxcXEZFXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHN0b3J5XFxcXFN0b3J5TGlzdC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Csrc%5C%5Ccomponents%5C%5Cstory%5C%5CStoryList.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/components/common/Pagination.tsx":
/*!**********************************************!*\
  !*** ./src/components/common/Pagination.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Pagination = ({ currentPage, totalPages, onPageChange, className = \"\" })=>{\n    const getVisiblePages = ()=>{\n        const delta = 2;\n        const range = [];\n        const rangeWithDots = [];\n        for(let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++){\n            range.push(i);\n        }\n        if (currentPage - delta > 2) {\n            rangeWithDots.push(1, \"...\");\n        } else {\n            rangeWithDots.push(1);\n        }\n        rangeWithDots.push(...range);\n        if (currentPage + delta < totalPages - 1) {\n            rangeWithDots.push(\"...\", totalPages);\n        } else {\n            rangeWithDots.push(totalPages);\n        }\n        return rangeWithDots;\n    };\n    if (totalPages <= 1) return null;\n    const visiblePages = getVisiblePages();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex items-center justify-center space-x-1 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                onClick: ()=>onPageChange(currentPage - 1),\n                disabled: currentPage === 1,\n                variant: \"outline\",\n                size: \"sm\",\n                className: \"rounded-r-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\common\\\\Pagination.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\common\\\\Pagination.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\common\\\\Pagination.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined),\n            visiblePages.map((page, index)=>{\n                if (page === \"...\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"outline\",\n                        size: \"sm\",\n                        className: \"rounded-none cursor-default\",\n                        disabled: true,\n                        children: \"...\"\n                    }, `dots-${index}`, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\common\\\\Pagination.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 13\n                    }, undefined);\n                }\n                const pageNumber = page;\n                const isActive = pageNumber === currentPage;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: ()=>onPageChange(pageNumber),\n                    variant: isActive ? \"default\" : \"outline\",\n                    size: \"sm\",\n                    className: \"rounded-none\",\n                    children: pageNumber\n                }, pageNumber, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\common\\\\Pagination.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 11\n                }, undefined);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                onClick: ()=>onPageChange(currentPage + 1),\n                disabled: currentPage === totalPages,\n                variant: \"outline\",\n                size: \"sm\",\n                className: \"rounded-l-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\common\\\\Pagination.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\common\\\\Pagination.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\common\\\\Pagination.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\common\\\\Pagination.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Pagination);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/common/Pagination.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/story/StoryCard.tsx":
/*!********************************************!*\
  !*** ./src/components/story/StoryCard.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_OptimizedImage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/OptimizedImage */ \"(ssr)/./src/components/ui/OptimizedImage.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n\n\n\n\n\nconst StoryCard = ({ story })=>{\n    const getStatusVariant = (status)=>{\n        switch(status){\n            case \"completed\":\n                return \"default\"; // Green variant\n            case \"ongoing\":\n                return \"secondary\"; // Blue variant\n            default:\n                return \"outline\"; // Yellow variant\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case \"completed\":\n                return \"Ho\\xe0n th\\xe0nh\";\n            case \"ongoing\":\n                return \"Đang ra\";\n            default:\n                return \"Tạm dừng\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        href: `/stories/${story.id}`,\n        className: \"group\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"overflow-hidden hover:shadow-xl transition-all duration-300 hover:scale-105 border-border/50 bg-card/50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative aspect-[3/4] bg-muted\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_OptimizedImage__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: story.cover_image_url,\n                            alt: story.title,\n                            fill: true,\n                            className: \"group-hover:scale-110 transition-transform duration-300\",\n                            sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\",\n                            priority: false,\n                            quality: 80\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryCard.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 right-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                variant: getStatusVariant(story.status),\n                                className: \"text-xs\",\n                                children: getStatusText(story.status)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryCard.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryCard.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryCard.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold text-foreground text-lg mb-2 group-hover:text-primary transition-colors overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"block truncate\",\n                                children: story.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryCard.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryCard.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground text-sm mb-2\",\n                            children: [\n                                \"T\\xe1c giả: \",\n                                story.author\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryCard.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground text-sm mb-3 overflow-hidden line-clamp-2\",\n                            children: story.description || \"Kh\\xf4ng c\\xf3 m\\xf4 tả\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryCard.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center text-xs text-muted-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: story.total_chapters ? `${story.total_chapters_scraped}/${story.total_chapters} chương` : `${story.total_chapters_scraped} chương`\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryCard.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: new Date(story.updated_at).toLocaleDateString(\"vi-VN\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryCard.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryCard.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryCard.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryCard.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryCard.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StoryCard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9zdG9yeS9TdG9yeUNhcmQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTZCO0FBQytCO0FBRUg7QUFDWDtBQU05QyxNQUFNSyxZQUFZLENBQUMsRUFBRUMsS0FBSyxFQUFrQjtJQUMxQyxNQUFNQyxtQkFBbUIsQ0FBQ0M7UUFDeEIsT0FBUUE7WUFDTixLQUFLO2dCQUNILE9BQU8sV0FBVyxnQkFBZ0I7WUFDcEMsS0FBSztnQkFDSCxPQUFPLGFBQWEsZUFBZTtZQUNyQztnQkFDRSxPQUFPLFdBQVcsaUJBQWlCO1FBQ3ZDO0lBQ0Y7SUFFQSxNQUFNQyxnQkFBZ0IsQ0FBQ0Q7UUFDckIsT0FBUUE7WUFDTixLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVDtnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUVBLHFCQUNFLDhEQUFDUixpREFBSUE7UUFBQ1UsTUFBTSxDQUFDLFNBQVMsRUFBRUosTUFBTUssRUFBRSxDQUFDLENBQUM7UUFBRUMsV0FBVTtrQkFDNUMsNEVBQUNWLHFEQUFJQTtZQUFDVSxXQUFVOzs4QkFDZCw4REFBQ0M7b0JBQUlELFdBQVU7O3NDQUNiLDhEQUFDWCxxRUFBY0E7NEJBQ2JhLEtBQUtSLE1BQU1TLGVBQWU7NEJBQzFCQyxLQUFLVixNQUFNVyxLQUFLOzRCQUNoQkMsSUFBSTs0QkFDSk4sV0FBVTs0QkFDVk8sT0FBTTs0QkFDTkMsVUFBVTs0QkFDVkMsU0FBUzs7Ozs7O3NDQUVYLDhEQUFDUjs0QkFBSUQsV0FBVTtzQ0FDYiw0RUFBQ1IsdURBQUtBO2dDQUFDa0IsU0FBU2YsaUJBQWlCRCxNQUFNRSxNQUFNO2dDQUFHSSxXQUFVOzBDQUN2REgsY0FBY0gsTUFBTUUsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBSWpDLDhEQUFDTCw0REFBV0E7b0JBQUNTLFdBQVU7O3NDQUNyQiw4REFBQ1c7NEJBQUdYLFdBQVU7c0NBQ1osNEVBQUNZO2dDQUFLWixXQUFVOzBDQUFrQk4sTUFBTVcsS0FBSzs7Ozs7Ozs7Ozs7c0NBRS9DLDhEQUFDUTs0QkFBRWIsV0FBVTs7Z0NBQXFDO2dDQUN0Q04sTUFBTW9CLE1BQU07Ozs7Ozs7c0NBRXhCLDhEQUFDRDs0QkFBRWIsV0FBVTtzQ0FDVk4sTUFBTXFCLFdBQVcsSUFBSTs7Ozs7O3NDQUV4Qiw4REFBQ2Q7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDWTs4Q0FDRWxCLE1BQU1zQixjQUFjLEdBQ25CLENBQUMsRUFBRXRCLE1BQU11QixzQkFBc0IsQ0FBQyxDQUFDLEVBQUV2QixNQUFNc0IsY0FBYyxDQUFDLE9BQU8sQ0FBQyxHQUVoRSxDQUFDLEVBQUV0QixNQUFNdUIsc0JBQXNCLENBQUMsT0FBTyxDQUFDOzs7Ozs7OENBRzVDLDhEQUFDTDs4Q0FBTSxJQUFJTSxLQUFLeEIsTUFBTXlCLFVBQVUsRUFBRUMsa0JBQWtCLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTWpFO0FBRUEsaUVBQWUzQixTQUFTQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmUvLi9zcmMvY29tcG9uZW50cy9zdG9yeS9TdG9yeUNhcmQudHN4P2EyZDkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJztcbmltcG9ydCBPcHRpbWl6ZWRJbWFnZSBmcm9tICdAL2NvbXBvbmVudHMvdWkvT3B0aW1pemVkSW1hZ2UnO1xuaW1wb3J0IHsgU3RvcnkgfSBmcm9tICdAL3R5cGVzL3N0b3J5JztcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnO1xuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYmFkZ2UnO1xuXG5pbnRlcmZhY2UgU3RvcnlDYXJkUHJvcHMge1xuICBzdG9yeTogU3Rvcnk7XG59XG5cbmNvbnN0IFN0b3J5Q2FyZCA9ICh7IHN0b3J5IH06IFN0b3J5Q2FyZFByb3BzKSA9PiB7XG4gIGNvbnN0IGdldFN0YXR1c1ZhcmlhbnQgPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSAnY29tcGxldGVkJzpcbiAgICAgICAgcmV0dXJuICdkZWZhdWx0JzsgLy8gR3JlZW4gdmFyaWFudFxuICAgICAgY2FzZSAnb25nb2luZyc6XG4gICAgICAgIHJldHVybiAnc2Vjb25kYXJ5JzsgLy8gQmx1ZSB2YXJpYW50XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gJ291dGxpbmUnOyAvLyBZZWxsb3cgdmFyaWFudFxuICAgIH1cbiAgfTtcblxuICBjb25zdCBnZXRTdGF0dXNUZXh0ID0gKHN0YXR1czogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChzdGF0dXMpIHtcbiAgICAgIGNhc2UgJ2NvbXBsZXRlZCc6XG4gICAgICAgIHJldHVybiAnSG/DoG4gdGjDoG5oJztcbiAgICAgIGNhc2UgJ29uZ29pbmcnOlxuICAgICAgICByZXR1cm4gJ8SQYW5nIHJhJztcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiAnVOG6oW0gZOG7q25nJztcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8TGluayBocmVmPXtgL3N0b3JpZXMvJHtzdG9yeS5pZH1gfSBjbGFzc05hbWU9XCJncm91cFwiPlxuICAgICAgPENhcmQgY2xhc3NOYW1lPVwib3ZlcmZsb3ctaGlkZGVuIGhvdmVyOnNoYWRvdy14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgaG92ZXI6c2NhbGUtMTA1IGJvcmRlci1ib3JkZXIvNTAgYmctY2FyZC81MFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGFzcGVjdC1bMy80XSBiZy1tdXRlZFwiPlxuICAgICAgICAgIDxPcHRpbWl6ZWRJbWFnZVxuICAgICAgICAgICAgc3JjPXtzdG9yeS5jb3Zlcl9pbWFnZV91cmx9XG4gICAgICAgICAgICBhbHQ9e3N0b3J5LnRpdGxlfVxuICAgICAgICAgICAgZmlsbFxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZ3JvdXAtaG92ZXI6c2NhbGUtMTEwIHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICBzaXplcz1cIihtYXgtd2lkdGg6IDc2OHB4KSAxMDB2dywgKG1heC13aWR0aDogMTIwMHB4KSA1MHZ3LCAzM3Z3XCJcbiAgICAgICAgICAgIHByaW9yaXR5PXtmYWxzZX1cbiAgICAgICAgICAgIHF1YWxpdHk9ezgwfVxuICAgICAgICAgIC8+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMiByaWdodC0yXCI+XG4gICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD17Z2V0U3RhdHVzVmFyaWFudChzdG9yeS5zdGF0dXMpfSBjbGFzc05hbWU9XCJ0ZXh0LXhzXCI+XG4gICAgICAgICAgICAgIHtnZXRTdGF0dXNUZXh0KHN0b3J5LnN0YXR1cyl9XG4gICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZm9yZWdyb3VuZCB0ZXh0LWxnIG1iLTIgZ3JvdXAtaG92ZXI6dGV4dC1wcmltYXJ5IHRyYW5zaXRpb24tY29sb3JzIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYmxvY2sgdHJ1bmNhdGVcIj57c3RvcnkudGl0bGV9PC9zcGFuPlxuICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kIHRleHQtc20gbWItMlwiPlxuICAgICAgICAgICAgVMOhYyBnaeG6ozoge3N0b3J5LmF1dGhvcn1cbiAgICAgICAgICA8L3A+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kIHRleHQtc20gbWItMyBvdmVyZmxvdy1oaWRkZW4gbGluZS1jbGFtcC0yXCI+XG4gICAgICAgICAgICB7c3RvcnkuZGVzY3JpcHRpb24gfHwgJ0tow7RuZyBjw7MgbcO0IHThuqMnfVxuICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciB0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgPHNwYW4+XG4gICAgICAgICAgICAgIHtzdG9yeS50b3RhbF9jaGFwdGVycyA/IChcbiAgICAgICAgICAgICAgICBgJHtzdG9yeS50b3RhbF9jaGFwdGVyc19zY3JhcGVkfS8ke3N0b3J5LnRvdGFsX2NoYXB0ZXJzfSBjaMawxqFuZ2BcbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICBgJHtzdG9yeS50b3RhbF9jaGFwdGVyc19zY3JhcGVkfSBjaMawxqFuZ2BcbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDxzcGFuPntuZXcgRGF0ZShzdG9yeS51cGRhdGVkX2F0KS50b0xvY2FsZURhdGVTdHJpbmcoJ3ZpLVZOJyl9PC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgPC9DYXJkPlxuICAgIDwvTGluaz5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFN0b3J5Q2FyZDsiXSwibmFtZXMiOlsiTGluayIsIk9wdGltaXplZEltYWdlIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQmFkZ2UiLCJTdG9yeUNhcmQiLCJzdG9yeSIsImdldFN0YXR1c1ZhcmlhbnQiLCJzdGF0dXMiLCJnZXRTdGF0dXNUZXh0IiwiaHJlZiIsImlkIiwiY2xhc3NOYW1lIiwiZGl2Iiwic3JjIiwiY292ZXJfaW1hZ2VfdXJsIiwiYWx0IiwidGl0bGUiLCJmaWxsIiwic2l6ZXMiLCJwcmlvcml0eSIsInF1YWxpdHkiLCJ2YXJpYW50IiwiaDMiLCJzcGFuIiwicCIsImF1dGhvciIsImRlc2NyaXB0aW9uIiwidG90YWxfY2hhcHRlcnMiLCJ0b3RhbF9jaGFwdGVyc19zY3JhcGVkIiwiRGF0ZSIsInVwZGF0ZWRfYXQiLCJ0b0xvY2FsZURhdGVTdHJpbmciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/story/StoryCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/story/StoryList.tsx":
/*!********************************************!*\
  !*** ./src/components/story/StoryList.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_storyService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/storyService */ \"(ssr)/./src/services/storyService.ts\");\n/* harmony import */ var _StoryCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./StoryCard */ \"(ssr)/./src/components/story/StoryCard.tsx\");\n/* harmony import */ var _components_common_Pagination__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/common/Pagination */ \"(ssr)/./src/components/common/Pagination.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(ssr)/./src/components/ui/skeleton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst StoryList = ()=>{\n    const [stories, setStories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalItems, setTotalItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const loadStories = async (pageNum = 1)=>{\n        try {\n            setLoading(true);\n            const response = await (0,_services_storyService__WEBPACK_IMPORTED_MODULE_2__.fetchStories)(pageNum, 12);\n            setStories(response.data);\n            setCurrentPage(pageNum);\n            setTotalPages(response.pagination.total_pages);\n            setTotalItems(response.pagination.total_items);\n            setError(null);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to fetch stories\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadStories(1);\n    }, []);\n    const handlePageChange = (pageNum)=>{\n        if (pageNum !== currentPage && !loading) {\n            loadStories(pageNum);\n            // Scroll to top when page changes\n            window.scrollTo({\n                top: 0,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    if (loading && stories.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mt-8\",\n            children: Array.from({\n                length: 8\n            }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                            className: \"aspect-[3/4] w-full\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-6 w-full mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-3/4 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-1/2 mb-3\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-3 w-16\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-3 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, index, true, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center mt-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"border-destructive/50 bg-destructive/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-destructive font-semibold mb-2\",\n                            children: \"Lỗi tải dữ liệu\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-destructive/80 mb-4\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                            onClick: ()=>loadStories(1),\n                            variant: \"destructive\",\n                            children: \"Thử lại\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (stories.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center mt-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                    className: \"p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-16 h-16 text-muted-foreground mx-auto mb-4\",\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 20 20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                fillRule: \"evenodd\",\n                                d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z\",\n                                clipRule: \"evenodd\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-foreground font-semibold mb-2\",\n                            children: \"Chưa c\\xf3 truyện n\\xe0o\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground mb-4\",\n                            children: \"H\\xe3y bắt đầu bằng c\\xe1ch c\\xe0o một truyện mới!\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/scrape\",\n                                children: \"C\\xe0o truyện mới\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n            lineNumber: 91,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-8\",\n        children: [\n            totalItems > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 text-sm\",\n                        children: [\n                            \"Hiển thị \",\n                            (currentPage - 1) * 12 + 1,\n                            \"-\",\n                            Math.min(currentPage * 12, totalItems),\n                            \" trong tổng số \",\n                            totalItems,\n                            \" truyện\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 text-sm\",\n                        children: [\n                            \"Trang \",\n                            currentPage,\n                            \" / \",\n                            totalPages\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                lineNumber: 114,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\",\n                children: stories.map((story)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StoryCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        story: story\n                    }, story.id, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, undefined),\n            totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_Pagination__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    currentPage: currentPage,\n                    totalPages: totalPages,\n                    onPageChange: handlePageChange,\n                    className: \"justify-center\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryList.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StoryList);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/story/StoryList.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/OptimizedImage.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/OptimizedImage.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst OptimizedImage = ({ src, alt, width, height, fill = false, className, sizes, priority = false, quality = 75, placeholder = \"empty\", blurDataURL, fallbackSrc = \"/images/placeholder.jpg\", showFallback = true, onError, ...props })=>{\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    // Handle cases where src is null, undefined, or empty\n    if (!src || imageError) {\n        if (!showFallback) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-center justify-center bg-zinc-700 text-gray-400\", className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-16 h-16\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\ui\\\\OptimizedImage.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\ui\\\\OptimizedImage.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\ui\\\\OptimizedImage.tsx\",\n                lineNumber: 48,\n                columnNumber: 9\n            }, undefined);\n        }\n        // Try to use fallback image\n        if (fallbackSrc && !imageError) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                src: fallbackSrc,\n                alt: alt,\n                width: width,\n                height: height,\n                fill: fill,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(className, \"object-cover\"),\n                sizes: sizes,\n                priority: priority,\n                quality: quality,\n                placeholder: placeholder,\n                blurDataURL: blurDataURL,\n                onError: ()=>{\n                    setImageError(true);\n                    onError?.();\n                },\n                onLoad: ()=>setIsLoading(false),\n                ...props\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\ui\\\\OptimizedImage.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, undefined);\n        }\n        // Final fallback - show placeholder\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-center justify-center bg-zinc-700 text-gray-400\", className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-16 h-16\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\",\n                    clipRule: \"evenodd\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\ui\\\\OptimizedImage.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\ui\\\\OptimizedImage.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\ui\\\\OptimizedImage.tsx\",\n            lineNumber: 90,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative w-full h-full\",\n        children: [\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute inset-0 flex items-center justify-center bg-zinc-700 animate-pulse\", className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 border-2 border-indigo-500 border-t-transparent rounded-full animate-spin\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\ui\\\\OptimizedImage.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\ui\\\\OptimizedImage.tsx\",\n                lineNumber: 108,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                src: src,\n                alt: alt,\n                width: width,\n                height: height,\n                fill: fill,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(className, \"object-cover transition-opacity duration-300\", isLoading ? \"opacity-0\" : \"opacity-100\"),\n                sizes: sizes,\n                priority: priority,\n                quality: quality,\n                placeholder: placeholder,\n                blurDataURL: blurDataURL,\n                onError: ()=>{\n                    setImageError(true);\n                    onError?.();\n                },\n                onLoad: ()=>setIsLoading(false),\n                ...props\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\ui\\\\OptimizedImage.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\ui\\\\OptimizedImage.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OptimizedImage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/OptimizedImage.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n            outline: \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2\",\n            sm: \"h-8 rounded-md px-3 text-xs\",\n            lg: \"h-10 rounded-md px-8\",\n            icon: \"h-9 w-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 47,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-xl border bg-card text-card-foreground shadow\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 48,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 68,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/skeleton.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/skeleton.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Skeleton: () => (/* binding */ Skeleton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\nfunction Skeleton({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"animate-pulse rounded-md bg-primary/10\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\ui\\\\skeleton.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9za2VsZXRvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBZ0M7QUFFaEMsU0FBU0MsU0FBUyxFQUNoQkMsU0FBUyxFQUNULEdBQUdDLE9BQ2tDO0lBQ3JDLHFCQUNFLDhEQUFDQztRQUNDRixXQUFXRiw4Q0FBRUEsQ0FBQywwQ0FBMENFO1FBQ3ZELEdBQUdDLEtBQUs7Ozs7OztBQUdmO0FBRW1CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmUvLi9zcmMvY29tcG9uZW50cy91aS9za2VsZXRvbi50c3g/MmE0MyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmZ1bmN0aW9uIFNrZWxldG9uKHtcbiAgY2xhc3NOYW1lLFxuICAuLi5wcm9wc1xufTogUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+KSB7XG4gIHJldHVybiAoXG4gICAgPGRpdlxuICAgICAgY2xhc3NOYW1lPXtjbihcImFuaW1hdGUtcHVsc2Ugcm91bmRlZC1tZCBiZy1wcmltYXJ5LzEwXCIsIGNsYXNzTmFtZSl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKVxufVxuXG5leHBvcnQgeyBTa2VsZXRvbiB9XG4iXSwibmFtZXMiOlsiY24iLCJTa2VsZXRvbiIsImNsYXNzTmFtZSIsInByb3BzIiwiZGl2Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/skeleton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL2ZlLy4vc3JjL2xpYi91dGlscy50cz83YzFjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/services/storyService.ts":
/*!**************************************!*\
  !*** ./src/services/storyService.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchChapterById: () => (/* binding */ fetchChapterById),\n/* harmony export */   fetchChapterByNumber: () => (/* binding */ fetchChapterByNumber),\n/* harmony export */   fetchChaptersByStoryId: () => (/* binding */ fetchChaptersByStoryId),\n/* harmony export */   fetchStories: () => (/* binding */ fetchStories),\n/* harmony export */   fetchStoryById: () => (/* binding */ fetchStoryById)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:8002\" || 0;\nconst API_PREFIX = \"/api/v1/stories\";\n// Fetch all stories with pagination\nconst fetchStories = async (page = 1, pageSize = 20)=>{\n    const response = await fetch(`${API_BASE_URL}${API_PREFIX}?page=${page}&page_size=${pageSize}`);\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch stories\");\n    }\n    return response.json();\n};\n// Fetch a single story by ID\nconst fetchStoryById = async (storyId)=>{\n    const response = await fetch(`${API_BASE_URL}${API_PREFIX}/${storyId}`);\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch story\");\n    }\n    return response.json();\n};\n// Fetch chapters for a story\nconst fetchChaptersByStoryId = async (storyId, page = 1, pageSize = 50, filters)=>{\n    const params = new URLSearchParams({\n        page: page.toString(),\n        page_size: pageSize.toString()\n    });\n    if (filters?.enhanced_only) {\n        params.append(\"enhanced_only\", \"true\");\n    }\n    if (filters?.scraped_only) {\n        params.append(\"scraped_only\", \"true\");\n    }\n    const response = await fetch(`${API_BASE_URL}${API_PREFIX}/${storyId}/chapters?${params.toString()}`);\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch chapters\");\n    }\n    return response.json();\n};\n// Fetch a single chapter by chapter number\nconst fetchChapterByNumber = async (storyId, chapterNumber)=>{\n    const response = await fetch(`${API_BASE_URL}${API_PREFIX}/${storyId}/chapters/${chapterNumber}`, {\n        cache: \"no-store\"\n    });\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch chapter\");\n    }\n    const data = await response.json();\n    // Backend returns ChapterContentResponse which extends APIResponse\n    // Extract the chapter data from the response structure\n    return {\n        id: data.id,\n        story_id: data.story_id,\n        chapter_number: data.chapter_number,\n        title: data.title,\n        url: data.url,\n        original_content: data.original_content,\n        enhanced_content: data.enhanced_content,\n        is_scraped: !!data.original_content,\n        is_enhanced: data.is_enhanced,\n        enhancement_status: data.enhancement_status,\n        word_count: data.word_count,\n        created_at: data.created_at,\n        updated_at: data.updated_at,\n        enhancement_metadata: data.enhancement_metadata\n    };\n};\n// Fetch a single chapter by ID\nconst fetchChapterById = async (chapterId)=>{\n    const response = await fetch(`${API_BASE_URL}/api/v1/chapters/${chapterId}`);\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch chapter\");\n    }\n    return response.json();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvc2VydmljZXMvc3RvcnlTZXJ2aWNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRUEsTUFBTUEsZUFBZUMsdUJBQStCLElBQUk7QUFDeEQsTUFBTUcsYUFBYTtBQUVuQixvQ0FBb0M7QUFDN0IsTUFBTUMsZUFBZSxPQUFPQyxPQUFlLENBQUMsRUFBRUMsV0FBbUIsRUFBRTtJQUN4RSxNQUFNQyxXQUFXLE1BQU1DLE1BQU0sQ0FBQyxFQUFFVCxhQUFhLEVBQUVJLFdBQVcsTUFBTSxFQUFFRSxLQUFLLFdBQVcsRUFBRUMsU0FBUyxDQUFDO0lBQzlGLElBQUksQ0FBQ0MsU0FBU0UsRUFBRSxFQUFFO1FBQ2hCLE1BQU0sSUFBSUMsTUFBTTtJQUNsQjtJQUVBLE9BQU9ILFNBQVNJLElBQUk7QUFDdEIsRUFBRTtBQUVGLDZCQUE2QjtBQUN0QixNQUFNQyxpQkFBaUIsT0FBT0M7SUFDbkMsTUFBTU4sV0FBVyxNQUFNQyxNQUFNLENBQUMsRUFBRVQsYUFBYSxFQUFFSSxXQUFXLENBQUMsRUFBRVUsUUFBUSxDQUFDO0lBRXRFLElBQUksQ0FBQ04sU0FBU0UsRUFBRSxFQUFFO1FBQ2hCLE1BQU0sSUFBSUMsTUFBTTtJQUNsQjtJQUVBLE9BQU9ILFNBQVNJLElBQUk7QUFDdEIsRUFBRTtBQUVGLDZCQUE2QjtBQUN0QixNQUFNRyx5QkFBeUIsT0FDcENELFNBQ0FSLE9BQWUsQ0FBQyxFQUNoQkMsV0FBbUIsRUFBRSxFQUNyQlM7SUFLQSxNQUFNQyxTQUFTLElBQUlDLGdCQUFnQjtRQUNqQ1osTUFBTUEsS0FBS2EsUUFBUTtRQUNuQkMsV0FBV2IsU0FBU1ksUUFBUTtJQUM5QjtJQUVBLElBQUlILFNBQVNLLGVBQWU7UUFDMUJKLE9BQU9LLE1BQU0sQ0FBQyxpQkFBaUI7SUFDakM7SUFDQSxJQUFJTixTQUFTTyxjQUFjO1FBQ3pCTixPQUFPSyxNQUFNLENBQUMsZ0JBQWdCO0lBQ2hDO0lBRUEsTUFBTWQsV0FBVyxNQUFNQyxNQUNyQixDQUFDLEVBQUVULGFBQWEsRUFBRUksV0FBVyxDQUFDLEVBQUVVLFFBQVEsVUFBVSxFQUFFRyxPQUFPRSxRQUFRLEdBQUcsQ0FBQztJQUd6RSxJQUFJLENBQUNYLFNBQVNFLEVBQUUsRUFBRTtRQUNoQixNQUFNLElBQUlDLE1BQU07SUFDbEI7SUFFQSxPQUFPSCxTQUFTSSxJQUFJO0FBQ3RCLEVBQUU7QUFFRiwyQ0FBMkM7QUFDcEMsTUFBTVksdUJBQXVCLE9BQU9WLFNBQWlCVztJQUMxRCxNQUFNakIsV0FBVyxNQUFNQyxNQUFNLENBQUMsRUFBRVQsYUFBYSxFQUFFSSxXQUFXLENBQUMsRUFBRVUsUUFBUSxVQUFVLEVBQUVXLGNBQWMsQ0FBQyxFQUFFO1FBQ2hHQyxPQUFPO0lBQ1Q7SUFHQSxJQUFJLENBQUNsQixTQUFTRSxFQUFFLEVBQUU7UUFDaEIsTUFBTSxJQUFJQyxNQUFNO0lBQ2xCO0lBRUEsTUFBTWdCLE9BQU8sTUFBTW5CLFNBQVNJLElBQUk7SUFDaEMsbUVBQW1FO0lBQ25FLHVEQUF1RDtJQUN2RCxPQUFPO1FBQ0xnQixJQUFJRCxLQUFLQyxFQUFFO1FBQ1hDLFVBQVVGLEtBQUtFLFFBQVE7UUFDdkJDLGdCQUFnQkgsS0FBS0csY0FBYztRQUNuQ0MsT0FBT0osS0FBS0ksS0FBSztRQUNqQkMsS0FBS0wsS0FBS0ssR0FBRztRQUNiQyxrQkFBa0JOLEtBQUtNLGdCQUFnQjtRQUN2Q0Msa0JBQWtCUCxLQUFLTyxnQkFBZ0I7UUFDdkNDLFlBQVksQ0FBQyxDQUFDUixLQUFLTSxnQkFBZ0I7UUFDbkNHLGFBQWFULEtBQUtTLFdBQVc7UUFDN0JDLG9CQUFvQlYsS0FBS1Usa0JBQWtCO1FBQzNDQyxZQUFZWCxLQUFLVyxVQUFVO1FBQzNCQyxZQUFZWixLQUFLWSxVQUFVO1FBQzNCQyxZQUFZYixLQUFLYSxVQUFVO1FBQzNCQyxzQkFBc0JkLEtBQUtjLG9CQUFvQjtJQUNqRDtBQUNGLEVBQUU7QUFFRiwrQkFBK0I7QUFDeEIsTUFBTUMsbUJBQW1CLE9BQU9DO0lBQ3JDLE1BQU1uQyxXQUFXLE1BQU1DLE1BQU0sQ0FBQyxFQUFFVCxhQUFhLGlCQUFpQixFQUFFMkMsVUFBVSxDQUFDO0lBRTNFLElBQUksQ0FBQ25DLFNBQVNFLEVBQUUsRUFBRTtRQUNoQixNQUFNLElBQUlDLE1BQU07SUFDbEI7SUFFQSxPQUFPSCxTQUFTSSxJQUFJO0FBQ3RCLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mZS8uL3NyYy9zZXJ2aWNlcy9zdG9yeVNlcnZpY2UudHM/NmZkNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTdG9yeSwgQ2hhcHRlciwgU3Rvcmllc1Jlc3BvbnNlLCBDaGFwdGVyc1Jlc3BvbnNlLCBTdG9yeVdpdGhDaGFwdGVycyB9IGZyb20gJ0AvdHlwZXMvc3RvcnknO1xuXG5jb25zdCBBUElfQkFTRV9VUkwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjgwMDInO1xuY29uc3QgQVBJX1BSRUZJWCA9ICcvYXBpL3YxL3N0b3JpZXMnO1xuXG4vLyBGZXRjaCBhbGwgc3RvcmllcyB3aXRoIHBhZ2luYXRpb25cbmV4cG9ydCBjb25zdCBmZXRjaFN0b3JpZXMgPSBhc3luYyAocGFnZTogbnVtYmVyID0gMSwgcGFnZVNpemU6IG51bWJlciA9IDIwKTogUHJvbWlzZTxTdG9yaWVzUmVzcG9uc2U+ID0+IHtcbiAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfQkFTRV9VUkx9JHtBUElfUFJFRklYfT9wYWdlPSR7cGFnZX0mcGFnZV9zaXplPSR7cGFnZVNpemV9YCk7XG4gIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBmZXRjaCBzdG9yaWVzJyk7XG4gIH1cbiAgXG4gIHJldHVybiByZXNwb25zZS5qc29uKCk7XG59O1xuXG4vLyBGZXRjaCBhIHNpbmdsZSBzdG9yeSBieSBJRFxuZXhwb3J0IGNvbnN0IGZldGNoU3RvcnlCeUlkID0gYXN5bmMgKHN0b3J5SWQ6IHN0cmluZyk6IFByb21pc2U8U3RvcnlXaXRoQ2hhcHRlcnM+ID0+IHtcbiAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfQkFTRV9VUkx9JHtBUElfUFJFRklYfS8ke3N0b3J5SWR9YCk7XG4gIFxuICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggc3RvcnknKTtcbiAgfVxuICBcbiAgcmV0dXJuIHJlc3BvbnNlLmpzb24oKTtcbn07XG5cbi8vIEZldGNoIGNoYXB0ZXJzIGZvciBhIHN0b3J5XG5leHBvcnQgY29uc3QgZmV0Y2hDaGFwdGVyc0J5U3RvcnlJZCA9IGFzeW5jIChcbiAgc3RvcnlJZDogc3RyaW5nLFxuICBwYWdlOiBudW1iZXIgPSAxLFxuICBwYWdlU2l6ZTogbnVtYmVyID0gNTAsXG4gIGZpbHRlcnM/OiB7XG4gICAgZW5oYW5jZWRfb25seT86IGJvb2xlYW47XG4gICAgc2NyYXBlZF9vbmx5PzogYm9vbGVhbjtcbiAgfVxuKTogUHJvbWlzZTxDaGFwdGVyc1Jlc3BvbnNlPiA9PiB7XG4gIGNvbnN0IHBhcmFtcyA9IG5ldyBVUkxTZWFyY2hQYXJhbXMoe1xuICAgIHBhZ2U6IHBhZ2UudG9TdHJpbmcoKSxcbiAgICBwYWdlX3NpemU6IHBhZ2VTaXplLnRvU3RyaW5nKClcbiAgfSk7XG5cbiAgaWYgKGZpbHRlcnM/LmVuaGFuY2VkX29ubHkpIHtcbiAgICBwYXJhbXMuYXBwZW5kKCdlbmhhbmNlZF9vbmx5JywgJ3RydWUnKTtcbiAgfVxuICBpZiAoZmlsdGVycz8uc2NyYXBlZF9vbmx5KSB7XG4gICAgcGFyYW1zLmFwcGVuZCgnc2NyYXBlZF9vbmx5JywgJ3RydWUnKTtcbiAgfVxuXG4gIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goXG4gICAgYCR7QVBJX0JBU0VfVVJMfSR7QVBJX1BSRUZJWH0vJHtzdG9yeUlkfS9jaGFwdGVycz8ke3BhcmFtcy50b1N0cmluZygpfWBcbiAgKTtcblxuICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggY2hhcHRlcnMnKTtcbiAgfVxuXG4gIHJldHVybiByZXNwb25zZS5qc29uKCk7XG59O1xuXG4vLyBGZXRjaCBhIHNpbmdsZSBjaGFwdGVyIGJ5IGNoYXB0ZXIgbnVtYmVyXG5leHBvcnQgY29uc3QgZmV0Y2hDaGFwdGVyQnlOdW1iZXIgPSBhc3luYyAoc3RvcnlJZDogc3RyaW5nLCBjaGFwdGVyTnVtYmVyOiBudW1iZXIpOiBQcm9taXNlPENoYXB0ZXI+ID0+IHtcbiAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfQkFTRV9VUkx9JHtBUElfUFJFRklYfS8ke3N0b3J5SWR9L2NoYXB0ZXJzLyR7Y2hhcHRlck51bWJlcn1gLCB7XG4gICAgY2FjaGU6ICduby1zdG9yZSdcbiAgfSk7XG4gIFxuXG4gIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBmZXRjaCBjaGFwdGVyJyk7XG4gIH1cbiAgXG4gIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gIC8vIEJhY2tlbmQgcmV0dXJucyBDaGFwdGVyQ29udGVudFJlc3BvbnNlIHdoaWNoIGV4dGVuZHMgQVBJUmVzcG9uc2VcbiAgLy8gRXh0cmFjdCB0aGUgY2hhcHRlciBkYXRhIGZyb20gdGhlIHJlc3BvbnNlIHN0cnVjdHVyZVxuICByZXR1cm4ge1xuICAgIGlkOiBkYXRhLmlkLFxuICAgIHN0b3J5X2lkOiBkYXRhLnN0b3J5X2lkLFxuICAgIGNoYXB0ZXJfbnVtYmVyOiBkYXRhLmNoYXB0ZXJfbnVtYmVyLFxuICAgIHRpdGxlOiBkYXRhLnRpdGxlLFxuICAgIHVybDogZGF0YS51cmwsXG4gICAgb3JpZ2luYWxfY29udGVudDogZGF0YS5vcmlnaW5hbF9jb250ZW50LFxuICAgIGVuaGFuY2VkX2NvbnRlbnQ6IGRhdGEuZW5oYW5jZWRfY29udGVudCxcbiAgICBpc19zY3JhcGVkOiAhIWRhdGEub3JpZ2luYWxfY29udGVudCxcbiAgICBpc19lbmhhbmNlZDogZGF0YS5pc19lbmhhbmNlZCxcbiAgICBlbmhhbmNlbWVudF9zdGF0dXM6IGRhdGEuZW5oYW5jZW1lbnRfc3RhdHVzLFxuICAgIHdvcmRfY291bnQ6IGRhdGEud29yZF9jb3VudCxcbiAgICBjcmVhdGVkX2F0OiBkYXRhLmNyZWF0ZWRfYXQsXG4gICAgdXBkYXRlZF9hdDogZGF0YS51cGRhdGVkX2F0LFxuICAgIGVuaGFuY2VtZW50X21ldGFkYXRhOiBkYXRhLmVuaGFuY2VtZW50X21ldGFkYXRhXG4gIH07XG59O1xuXG4vLyBGZXRjaCBhIHNpbmdsZSBjaGFwdGVyIGJ5IElEXG5leHBvcnQgY29uc3QgZmV0Y2hDaGFwdGVyQnlJZCA9IGFzeW5jIChjaGFwdGVySWQ6IHN0cmluZyk6IFByb21pc2U8Q2hhcHRlcj4gPT4ge1xuICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke0FQSV9CQVNFX1VSTH0vYXBpL3YxL2NoYXB0ZXJzLyR7Y2hhcHRlcklkfWApO1xuICBcbiAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGZldGNoIGNoYXB0ZXInKTtcbiAgfVxuICBcbiAgcmV0dXJuIHJlc3BvbnNlLmpzb24oKTtcbn07Il0sIm5hbWVzIjpbIkFQSV9CQVNFX1VSTCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BUElfVVJMIiwiQVBJX1BSRUZJWCIsImZldGNoU3RvcmllcyIsInBhZ2UiLCJwYWdlU2l6ZSIsInJlc3BvbnNlIiwiZmV0Y2giLCJvayIsIkVycm9yIiwianNvbiIsImZldGNoU3RvcnlCeUlkIiwic3RvcnlJZCIsImZldGNoQ2hhcHRlcnNCeVN0b3J5SWQiLCJmaWx0ZXJzIiwicGFyYW1zIiwiVVJMU2VhcmNoUGFyYW1zIiwidG9TdHJpbmciLCJwYWdlX3NpemUiLCJlbmhhbmNlZF9vbmx5IiwiYXBwZW5kIiwic2NyYXBlZF9vbmx5IiwiZmV0Y2hDaGFwdGVyQnlOdW1iZXIiLCJjaGFwdGVyTnVtYmVyIiwiY2FjaGUiLCJkYXRhIiwiaWQiLCJzdG9yeV9pZCIsImNoYXB0ZXJfbnVtYmVyIiwidGl0bGUiLCJ1cmwiLCJvcmlnaW5hbF9jb250ZW50IiwiZW5oYW5jZWRfY29udGVudCIsImlzX3NjcmFwZWQiLCJpc19lbmhhbmNlZCIsImVuaGFuY2VtZW50X3N0YXR1cyIsIndvcmRfY291bnQiLCJjcmVhdGVkX2F0IiwidXBkYXRlZF9hdCIsImVuaGFuY2VtZW50X21ldGFkYXRhIiwiZmV0Y2hDaGFwdGVyQnlJZCIsImNoYXB0ZXJJZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/services/storyService.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e8043e36ccac\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmUvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzk5ODYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlODA0M2UzNmNjYWNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_layout_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Navbar */ \"(rsc)/./src/components/layout/Navbar.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Footer */ \"(rsc)/./src/components/layout/Footer.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"WebTruyen - Your Story Hub\",\n    description: \"A modern web application for scraping and reading stories.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className)} flex flex-col h-full`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-grow\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQU1NQTtBQUppQjtBQUN5QjtBQUNBO0FBSXpDLE1BQU1HLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdSO0lBQ0EscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0MsV0FBVTtrQkFDeEIsNEVBQUNDO1lBQUtELFdBQVcsQ0FBQyxFQUFFViwrSkFBZSxDQUFDLHFCQUFxQixDQUFDOzs4QkFDeEQsOERBQUNDLGlFQUFNQTs7Ozs7OEJBQ1AsOERBQUNXO29CQUFLRixXQUFVOzhCQUFhSDs7Ozs7OzhCQUM3Qiw4REFBQ0wsaUVBQU1BOzs7Ozs7Ozs7Ozs7Ozs7O0FBSWYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mZS8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuaW1wb3J0IE5hdmJhciBmcm9tIFwiQC9jb21wb25lbnRzL2xheW91dC9OYXZiYXJcIjtcbmltcG9ydCBGb290ZXIgZnJvbSBcIkAvY29tcG9uZW50cy9sYXlvdXQvRm9vdGVyXCI7XG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbXCJsYXRpblwiXSB9KTtcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6IFwiV2ViVHJ1eWVuIC0gWW91ciBTdG9yeSBIdWJcIixcbiAgZGVzY3JpcHRpb246IFwiQSBtb2Rlcm4gd2ViIGFwcGxpY2F0aW9uIGZvciBzY3JhcGluZyBhbmQgcmVhZGluZyBzdG9yaWVzLlwiLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIiBjbGFzc05hbWU9XCJoLWZ1bGwgZGFya1wiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtgJHtpbnRlci5jbGFzc05hbWV9IGZsZXggZmxleC1jb2wgaC1mdWxsYH0+XG4gICAgICAgIDxOYXZiYXIgLz5cbiAgICAgICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleC1ncm93XCI+e2NoaWxkcmVufTwvbWFpbj5cbiAgICAgICAgPEZvb3RlciAvPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn0iXSwibmFtZXMiOlsiaW50ZXIiLCJOYXZiYXIiLCJGb290ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImNsYXNzTmFtZSIsImJvZHkiLCJtYWluIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/loading.tsx":
/*!*****************************!*\
  !*** ./src/app/loading.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-500 mb-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-400\",\n                    children: \"Đang tải...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 6,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\loading.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\loading.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xvYWRpbmcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZSxTQUFTQTtJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOzs7Ozs7OEJBQ2YsOERBQUNDO29CQUFFRCxXQUFVOzhCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJckMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mZS8uL3NyYy9hcHAvbG9hZGluZy50c3g/OWNkOSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2FkaW5nKCkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaW5saW5lLWJsb2NrIGFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0xMiB3LTEyIGJvcmRlci1iLTIgYm9yZGVyLWluZGlnby01MDAgbWItNFwiPjwvZGl2PlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+xJBhbmcgdOG6o2kuLi48L3A+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn0iXSwibmFtZXMiOlsiTG9hZGluZyIsImRpdiIsImNsYXNzTmFtZSIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-24 h-24 text-gray-400 mx-auto mb-4\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 1,\n                                d: \"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.562M15 6.306a7.962 7.962 0 00-6 0m6 0V5a2 2 0 00-2-2H9a2 2 0 00-2 2v1.306m8 0V7a2 2 0 012 2v6.414c0 .796-.316 1.559-.879 2.121l-7.07 7.071a2 2 0 01-2.829 0L6.151 17.535A2.99 2.99 0 015.272 15.5L12 8.772l6.728 6.728a2.99 2.99 0 01-.879 2.121z\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 8,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-6xl font-bold text-white mb-4\",\n                            children: \"404\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-gray-300 mb-4\",\n                            children: \"Kh\\xf4ng t\\xecm thấy trang\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 mb-8 max-w-md mx-auto\",\n                            children: \"Trang bạn đang t\\xecm kiếm c\\xf3 thể đ\\xe3 bị x\\xf3a, đổi t\\xean hoặc tạm thời kh\\xf4ng khả dụng.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/\",\n                            className: \"inline-flex items-center px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 mr-2\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this),\n                                \"Về trang chủ\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/scrape\",\n                                className: \"inline-flex items-center px-4 py-2 text-indigo-400 hover:text-indigo-300 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"C\\xe0o truyện mới\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_story_StoryList__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/story/StoryList */ \"(rsc)/./src/components/story/StoryList.tsx\");\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-4xl font-bold\",\n                children: \"Story Dashboard\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-4 text-lg text-gray-300\",\n                children: \"Welcome to your scraped story collection.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_story_StoryList__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXFEO0FBRXRDLFNBQVNDO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0M7Z0JBQUdELFdBQVU7MEJBQXFCOzs7Ozs7MEJBQ25DLDhEQUFDRTtnQkFBRUYsV0FBVTswQkFBNkI7Ozs7OzswQkFDMUMsOERBQUNILG1FQUFTQTs7Ozs7Ozs7Ozs7QUFHaEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mZS8uL3NyYy9hcHAvcGFnZS50c3g/ZjY4YSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgU3RvcnlMaXN0IGZyb20gJ0AvY29tcG9uZW50cy9zdG9yeS9TdG9yeUxpc3QnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS04XCI+XG4gICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC00eGwgZm9udC1ib2xkXCI+U3RvcnkgRGFzaGJvYXJkPC9oMT5cbiAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTQgdGV4dC1sZyB0ZXh0LWdyYXktMzAwXCI+V2VsY29tZSB0byB5b3VyIHNjcmFwZWQgc3RvcnkgY29sbGVjdGlvbi48L3A+XG4gICAgICA8U3RvcnlMaXN0IC8+XG4gICAgPC9kaXY+XG4gICk7XG59Il0sIm5hbWVzIjpbIlN0b3J5TGlzdCIsIkhvbWUiLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Footer = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-zinc-800/30 border-t border-gray-700 mt-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-sm text-gray-400\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        \"\\xa9 \",\n                        new Date().getFullYear(),\n                        \" WebTruyen. All rights reserved.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 6,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 5,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvRm9vdGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUEsTUFBTUEsU0FBUztJQUNiLHFCQUNFLDhEQUFDQztRQUFPQyxXQUFVO2tCQUNoQiw0RUFBQ0M7WUFBSUQsV0FBVTtzQkFDYiw0RUFBQ0M7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNFOzt3QkFBRTt3QkFBUSxJQUFJQyxPQUFPQyxXQUFXO3dCQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSzlDO0FBRUEsaUVBQWVOLE1BQU1BLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mZS8uL3NyYy9jb21wb25lbnRzL2xheW91dC9Gb290ZXIudHN4PzI2MzgiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgRm9vdGVyID0gKCkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxmb290ZXIgY2xhc3NOYW1lPVwiYmctemluYy04MDAvMzAgYm9yZGVyLXQgYm9yZGVyLWdyYXktNzAwIG10LWF1dG9cIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHktNCBweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHRleHQtc20gdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgIDxwPiZjb3B5OyB7bmV3IERhdGUoKS5nZXRGdWxsWWVhcigpfSBXZWJUcnV5ZW4uIEFsbCByaWdodHMgcmVzZXJ2ZWQuPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZm9vdGVyPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgRm9vdGVyOyJdLCJuYW1lcyI6WyJGb290ZXIiLCJmb290ZXIiLCJjbGFzc05hbWUiLCJkaXYiLCJwIiwiRGF0ZSIsImdldEZ1bGxZZWFyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/Navbar.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Navbar.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n\n\nconst Navbar = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-zinc-800/30 border-b border-gray-700 backdrop-blur-2xl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/\",\n                            className: \"text-white font-bold text-xl\",\n                            children: \"WebTruyen\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:block\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-10 flex items-baseline space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"text-gray-300 hover:bg-zinc-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium\",\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/scrape\",\n                                    className: \"text-gray-300 hover:bg-zinc-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium\",\n                                    children: \"Scrape\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                lineNumber: 7,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navbar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvTmF2YmFyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUE2QjtBQUU3QixNQUFNQyxTQUFTO0lBQ2IscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNDO1lBQUlELFdBQVU7c0JBQ2IsNEVBQUNDO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ0M7d0JBQUlELFdBQVU7a0NBQ2IsNEVBQUNILGlEQUFJQTs0QkFBQ0ssTUFBSzs0QkFBSUYsV0FBVTtzQ0FBK0I7Ozs7Ozs7Ozs7O2tDQUkxRCw4REFBQ0M7d0JBQUlELFdBQVU7a0NBQ2IsNEVBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ0gsaURBQUlBO29DQUFDSyxNQUFLO29DQUFJRixXQUFVOzhDQUE0Rjs7Ozs7OzhDQUdySCw4REFBQ0gsaURBQUlBO29DQUFDSyxNQUFLO29DQUFVRixXQUFVOzhDQUE0Rjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBU3pJO0FBRUEsaUVBQWVGLE1BQU1BLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mZS8uL3NyYy9jb21wb25lbnRzL2xheW91dC9OYXZiYXIudHN4Pzc3YjkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJztcblxuY29uc3QgTmF2YmFyID0gKCkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxuYXYgY2xhc3NOYW1lPVwiYmctemluYy04MDAvMzAgYm9yZGVyLWIgYm9yZGVyLWdyYXktNzAwIGJhY2tkcm9wLWJsdXItMnhsXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIGgtMTZcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1ib2xkIHRleHQteGxcIj5cbiAgICAgICAgICAgICAgV2ViVHJ1eWVuXG4gICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoaWRkZW4gbWQ6YmxvY2tcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtMTAgZmxleCBpdGVtcy1iYXNlbGluZSBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9cIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIGhvdmVyOmJnLXppbmMtNzAwIGhvdmVyOnRleHQtd2hpdGUgcHgtMyBweS0yIHJvdW5kZWQtbWQgdGV4dC1zbSBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgIERhc2hib2FyZFxuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvc2NyYXBlXCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBob3ZlcjpiZy16aW5jLTcwMCBob3Zlcjp0ZXh0LXdoaXRlIHB4LTMgcHktMiByb3VuZGVkLW1kIHRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICBTY3JhcGVcbiAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9uYXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBOYXZiYXI7Il0sIm5hbWVzIjpbIkxpbmsiLCJOYXZiYXIiLCJuYXYiLCJjbGFzc05hbWUiLCJkaXYiLCJocmVmIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/Navbar.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/story/StoryList.tsx":
/*!********************************************!*\
  !*** ./src/components/story/StoryList.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Personal Projects\Vibe\Webtruyen\FE\src\components\story\StoryList.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/class-variance-authority","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CPersonal%20Projects%5CVibe%5CWebtruyen%5CFE%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CPersonal%20Projects%5CVibe%5CWebtruyen%5CFE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();