globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/stories/[storyId]/[chapterId]/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/components/story/StoryList.tsx":{"*":{"id":"(ssr)/./src/components/story/StoryList.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/story/EnhancedChapterList.tsx":{"*":{"id":"(ssr)/./src/components/story/EnhancedChapterList.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/story/StoryInfo.tsx":{"*":{"id":"(ssr)/./src/components/story/StoryInfo.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/reader/ChapterReaderWrapper.tsx":{"*":{"id":"(ssr)/./src/components/reader/ChapterReaderWrapper.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\src\\components\\story\\StoryList.tsx":{"id":"(app-pages-browser)/./src/components/story/StoryList.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\node_modules\\next\\dist\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\node_modules\\next\\dist\\esm\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\src\\components\\story\\EnhancedChapterList.tsx":{"id":"(app-pages-browser)/./src/components/story/EnhancedChapterList.tsx","name":"*","chunks":["app/stories/[storyId]/page","static/chunks/app/stories/%5BstoryId%5D/page.js"],"async":false},"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\src\\components\\story\\StoryInfo.tsx":{"id":"(app-pages-browser)/./src/components/story/StoryInfo.tsx","name":"*","chunks":["app/stories/[storyId]/page","static/chunks/app/stories/%5BstoryId%5D/page.js"],"async":false},"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\src\\components\\reader\\ChapterReaderWrapper.tsx":{"id":"(app-pages-browser)/./src/components/reader/ChapterReaderWrapper.tsx","name":"*","chunks":["app/stories/[storyId]/[chapterId]/page","static/chunks/app/stories/%5BstoryId%5D/%5BchapterId%5D/page.js"],"async":false}},"entryCSSFiles":{"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\src\\":[],"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\src\\app\\page":[],"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\src\\app\\layout":["static/css/app/layout.css"],"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\src\\app\\loading":[],"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\src\\app\\not-found":[],"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\src\\app\\stories\\[storyId]\\page":[],"D:\\Personal Projects\\Vibe\\Webtruyen\\FE\\src\\app\\stories\\[storyId]\\[chapterId]\\page":[]}}